<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Force slider to work correctly in RTL */
        #simple-slider {
            direction: ltr !important;
        }
        #simple-slider > div {
            flex-shrink: 0;
            width: 100%;
            direction: rtl !important; /* النصوص العربية تحتاج RTL */
        }
        .slider-container {
            direction: ltr !important;
        }
        .slide-content {
            direction: rtl !important;
            text-align: center !important;
            color: white !important;
            z-index: 10;
            position: relative;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-4xl font-bold text-center mb-8 text-blue-600">اختبار السلايد البسيط</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4">معلومات الاختبار:</h2>
            <p><strong>اللغة:</strong> {{ $locale ?? 'غير محدد' }}</p>
            <p><strong>عربي:</strong> {{ $isArabic ? 'نعم' : 'لا' }}</p>
            <p><strong>عدد المعارض:</strong> {{ count($sliderImages ?? []) }}</p>
        </div>

        @if(isset($sliderImages) && count($sliderImages) > 0)
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-4">المعارض:</h2>
                
                @foreach($sliderImages as $index => $slide)
                    <div class="border-l-4 border-blue-500 pl-4 mb-4 p-4 bg-gray-50">
                        <h3 class="text-xl font-bold text-blue-600">{{ $index + 1 }}. {{ $slide['title'] ?? 'بدون عنوان' }}</h3>
                        <p class="text-gray-600 mt-2">{{ $slide['description'] ?? 'بدون وصف' }}</p>
                        <p class="text-sm text-gray-500 mt-2">
                            <strong>زر:</strong> {{ $slide['button_text'] ?? 'بدون نص' }} | 
                            <strong>لون:</strong> {{ $slide['background_color'] ?? 'بدون لون' }}
                        </p>
                    </div>
                @endforeach
            </div>
            
            <!-- Simple Slider Test -->
            <div class="mt-8 bg-white rounded-lg shadow-lg overflow-hidden">
                <h2 class="text-2xl font-bold p-6 bg-blue-600 text-white">اختبار السلايد:</h2>
                
                <div class="relative h-64 overflow-hidden slider-container">
                    <div class="flex transition-transform duration-500 ease-in-out h-full" id="simple-slider" style="direction: ltr !important; width: {{ count($sliderImages) * 100 }}%;">
                        @foreach($sliderImages as $index => $slide)
                            <div class="min-w-full relative h-full flex items-center justify-center"
                                 style="background: {{ $slide['background_color'] ?? '#1e40af' }};">
                                <!-- خلفية شبه شفافة للنصوص -->
                                <div class="absolute inset-0 bg-black bg-opacity-40"></div>

                                <div class="slide-content relative z-20 bg-black bg-opacity-50 p-6 rounded-lg">
                                    <h3 class="text-3xl font-bold mb-4 text-white">{{ $slide['title'] ?? 'بدون عنوان' }}</h3>
                                    <p class="text-xl text-white mb-2">{{ $slide['description'] ?? 'بدون وصف' }}</p>
                                    <p class="text-lg text-yellow-300 font-bold">معرض رقم {{ $index + 1 }} من {{ count($sliderImages) }}</p>
                                    <div class="mt-4">
                                        <button class="bg-white text-gray-800 px-6 py-2 rounded-lg font-bold hover:bg-gray-100 shadow-lg">
                                            {{ $slide['button_text'] ?? 'عرض التفاصيل' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Simple Controls -->
                    <button onclick="previousSlide()" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 text-xl font-bold">
                        ←
                    </button>
                    <button onclick="nextSlide()" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 text-xl font-bold">
                        →
                    </button>

                    <!-- Auto-play control -->
                    <button onclick="toggleAutoPlay()" id="autoplay-btn" class="absolute top-4 right-4 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-lg px-3 py-1 text-sm font-bold">
                        إيقاف التلقائي
                    </button>
                    
                    <!-- Indicators -->
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        @foreach($sliderImages as $index => $slide)
                            <button onclick="goToSlide({{ $index }})" 
                                    class="w-3 h-3 rounded-full bg-white {{ $index === 0 ? 'bg-opacity-100' : 'bg-opacity-50' }}" 
                                    id="indicator-{{ $index }}"></button>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <strong>خطأ:</strong> لا توجد معارض للعرض!
            </div>
        @endif
        
        <div class="mt-8 text-center">
            <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 mr-4">الصفحة الرئيسية</a>
            <a href="/homepage-alt" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700">الصفحة البديلة</a>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = {{ count($sliderImages ?? []) }};
        let autoPlayInterval = null;
        let isAutoPlaying = true;
        
        function updateSlider() {
            const slider = document.getElementById('simple-slider');
            if (slider) {
                // Force LTR direction for slider movement regardless of page direction
                const translateX = -currentSlide * 100;
                slider.style.transform = `translateX(${translateX}%)`;
                slider.style.direction = 'ltr'; // Force LTR for slider container

                // Update indicators
                for (let i = 0; i < totalSlides; i++) {
                    const indicator = document.getElementById(`indicator-${i}`);
                    if (indicator) {
                        if (i === currentSlide) {
                            indicator.classList.remove('bg-opacity-50');
                            indicator.classList.add('bg-opacity-100');
                        } else {
                            indicator.classList.remove('bg-opacity-100');
                            indicator.classList.add('bg-opacity-50');
                        }
                    }
                }

                console.log(`Moved to slide ${currentSlide}, translateX: ${translateX}%`);
            }
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateSlider();
        }
        
        function previousSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            updateSlider();
        }
        
        function goToSlide(index) {
            currentSlide = index;
            updateSlider();
        }

        function toggleAutoPlay() {
            const btn = document.getElementById('autoplay-btn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = 'تشغيل التلقائي';
                btn.classList.add('bg-red-200');
            } else {
                startAutoPlay();
                isAutoPlaying = true;
                btn.textContent = 'إيقاف التلقائي';
                btn.classList.remove('bg-red-200');
            }
        }

        function startAutoPlay() {
            if (totalSlides > 1) {
                autoPlayInterval = setInterval(nextSlide, 8000); // 8 seconds
            }
        }

        // Start auto-play initially
        startAutoPlay();
        
        // Initialize
        updateSlider();

        console.log('Simple slider initialized with', totalSlides, 'slides');
        console.log('Slider element:', document.getElementById('simple-slider'));
        console.log('Slider width:', document.getElementById('simple-slider')?.style.width);

        // Test buttons
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, slider ready');
        });
    </script>
</body>
</html>
