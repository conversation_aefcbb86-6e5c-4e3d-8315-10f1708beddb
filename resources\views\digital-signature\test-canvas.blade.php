<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>اختبار التوقيع الإلكتروني</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .signature-canvas-container {
            position: relative;
            background: white;
            border-radius: 8px;
            margin: 0 auto;
            max-width: 600px;
        }
        #signatureCanvas {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
            display: block;
            margin: 0 auto;
            width: 100%;
            height: 200px;
        }
        #signatureCanvas:hover {
            border-color: #3b82f6;
        }
        .coordinates-display {
            font-family: monospace;
            font-size: 12px;
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <h1 class="text-2xl font-bold text-center mb-8">🧪 اختبار التوقيع الإلكتروني</h1>
        
        <!-- Canvas Container -->
        <div class="mb-8">
            <h3 class="font-bold text-gray-900 mb-4">✍️ منطقة التوقيع</h3>
            <div class="border-2 border-gray-300 rounded-lg p-4">
                <div class="signature-canvas-container">
                    <canvas id="signatureCanvas" 
                            width="600" 
                            height="200"></canvas>
                </div>
                
                <!-- Controls -->
                <div class="mt-4 flex justify-between items-center">
                    <button type="button" id="clearSignature"
                            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                        🗑️ مسح التوقيع
                    </button>
                    
                    <button type="button" id="saveSignature"
                            class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        💾 حفظ التوقيع
                    </button>
                    
                    <span class="text-sm text-gray-600">يرجى التوقيع في المربع أعلاه</span>
                </div>
            </div>
        </div>

        <!-- Debug Information -->
        <div class="mb-8">
            <h3 class="font-bold text-gray-900 mb-4">🔍 معلومات التشخيص</h3>
            <div class="coordinates-display">
                <div><strong>Canvas Size:</strong> <span id="canvasSize">-</span></div>
                <div><strong>Display Size:</strong> <span id="displaySize">-</span></div>
                <div><strong>Mouse Position:</strong> <span id="mousePos">-</span></div>
                <div><strong>Canvas Coordinates:</strong> <span id="canvasCoords">-</span></div>
                <div><strong>Scale Factor:</strong> <span id="scaleFactor">-</span></div>
                <div><strong>Has Signature:</strong> <span id="hasSignature">لا</span></div>
            </div>
        </div>

        <!-- Signature Preview -->
        <div class="mb-8">
            <h3 class="font-bold text-gray-900 mb-4">👁️ معاينة التوقيع</h3>
            <div id="signaturePreview" class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-500">
                لا يوجد توقيع بعد
            </div>
        </div>

        <!-- Back Button -->
        <div class="text-center">
            <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                ← العودة للصفحة الرئيسية
            </a>
        </div>
    </div>

    <script>
        // Canvas setup
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let hasSignature = false;

        // Debug elements
        const canvasSizeEl = document.getElementById('canvasSize');
        const displaySizeEl = document.getElementById('displaySize');
        const mousePosEl = document.getElementById('mousePos');
        const canvasCoordsEl = document.getElementById('canvasCoords');
        const scaleFactorEl = document.getElementById('scaleFactor');
        const hasSignatureEl = document.getElementById('hasSignature');

        // Setup canvas
        function setupCanvas() {
            // Set actual canvas size
            canvas.width = 600;
            canvas.height = 200;
            
            // Set drawing properties
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            updateDebugInfo();
        }

        // Update debug information
        function updateDebugInfo() {
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            
            canvasSizeEl.textContent = `${canvas.width} x ${canvas.height}`;
            displaySizeEl.textContent = `${Math.round(rect.width)} x ${Math.round(rect.height)}`;
            scaleFactorEl.textContent = `X: ${scaleX.toFixed(2)}, Y: ${scaleY.toFixed(2)}`;
            hasSignatureEl.textContent = hasSignature ? 'نعم' : 'لا';
        }

        // Get canvas coordinates
        function getCanvasCoordinates(e) {
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            
            const coords = {
                x: (e.clientX - rect.left) * scaleX,
                y: (e.clientY - rect.top) * scaleY
            };
            
            // Update debug info
            mousePosEl.textContent = `${e.clientX - rect.left}, ${e.clientY - rect.top}`;
            canvasCoordsEl.textContent = `${Math.round(coords.x)}, ${Math.round(coords.y)}`;
            
            return coords;
        }

        // Drawing functions
        function startDrawing(e) {
            isDrawing = true;
            const coords = getCanvasCoordinates(e);
            ctx.beginPath();
            ctx.moveTo(coords.x, coords.y);
        }

        function draw(e) {
            if (!isDrawing) return;
            const coords = getCanvasCoordinates(e);
            ctx.lineTo(coords.x, coords.y);
            ctx.stroke();
            hasSignature = true;
            updateDebugInfo();
        }

        function stopDrawing() {
            isDrawing = false;
        }

        // Touch handling
        function handleTouch(e) {
            e.preventDefault();
            if (e.touches && e.touches.length > 0) {
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                                e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }
        }

        // Event listeners
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);
        canvas.addEventListener('mousemove', function(e) {
            if (!isDrawing) {
                getCanvasCoordinates(e); // Update debug info even when not drawing
            }
        });

        // Touch events
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopDrawing);

        // Clear signature
        document.getElementById('clearSignature').addEventListener('click', function() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            hasSignature = false;
            updateDebugInfo();
            document.getElementById('signaturePreview').innerHTML = '<span class="text-gray-500">لا يوجد توقيع بعد</span>';
        });

        // Save signature
        document.getElementById('saveSignature').addEventListener('click', function() {
            if (!hasSignature) {
                alert('يرجى الرسم أولاً!');
                return;
            }
            
            const dataURL = canvas.toDataURL();
            const img = document.createElement('img');
            img.src = dataURL;
            img.style.maxWidth = '100%';
            img.style.border = '1px solid #ccc';
            img.style.borderRadius = '4px';
            
            document.getElementById('signaturePreview').innerHTML = '';
            document.getElementById('signaturePreview').appendChild(img);
        });

        // Initialize
        setupCanvas();
        
        // Update debug info on resize
        window.addEventListener('resize', function() {
            setTimeout(updateDebugInfo, 100);
        });
    </script>
</body>
</html>
