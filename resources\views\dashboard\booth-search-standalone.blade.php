<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('البحث عن الأجنحة') }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
        }
        .booth-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background: white;
        }
        .booth-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .btn-primary:hover {
            background-color: #2563eb;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 9999px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-available {
            background-color: #d1fae5;
            color: #065f46;
        }
        @media print {
            nav, .no-print { display: none !important; }
            body { background: white !important; }
            .booth-card { border: 1px solid #ccc !important; box-shadow: none !important; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                        <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                    </a>
                </div>

                <div class="flex items-center space-x-4" style="direction: ltr;">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حسابي') }}
                    </a>
                    <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حجوزاتي') }}
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">{{ __('البحث عن الأجنحة') }}</h1>
                <p class="mt-2 text-gray-600">{{ __('ابحث عن الجناح المثالي لمعرضك') }}</p>
            </div>

            <!-- Search Filters -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8 no-print">
                <form method="GET" action="{{ route('dashboard.booth-search') }}">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <!-- Exhibition Filter -->
                        <div>
                            <label for="exhibition_id" class="block text-sm font-medium text-gray-700 mb-2">{{ __('المعرض') }}</label>
                            <select name="exhibition_id" id="exhibition_id" class="form-input">
                                <option value="">{{ __('جميع المعارض') }}</option>
                                @foreach($exhibitions as $exhibition)
                                    <option value="{{ $exhibition->id }}" {{ request('exhibition_id') == $exhibition->id ? 'selected' : '' }}>
                                        {{ $exhibition->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Price Range -->
                        <div>
                            <label for="min_price" class="block text-sm font-medium text-gray-700 mb-2">{{ __('السعر الأدنى') }}</label>
                            <input type="number" name="min_price" id="min_price" value="{{ request('min_price') }}"
                                   class="form-input" placeholder="0">
                        </div>

                        <div>
                            <label for="max_price" class="block text-sm font-medium text-gray-700 mb-2">{{ __('السعر الأعلى') }}</label>
                            <input type="number" name="max_price" id="max_price" value="{{ request('max_price') }}"
                                   class="form-input" placeholder="10000">
                        </div>

                        <!-- Size Filter -->
                        <div>
                            <label for="size" class="block text-sm font-medium text-gray-700 mb-2">{{ __('الحجم') }}</label>
                            <select name="size" id="size" class="form-input">
                                <option value="">{{ __('جميع الأحجام') }}</option>
                                <option value="small" {{ request('size') == 'small' ? 'selected' : '' }}>{{ __('صغير') }}</option>
                                <option value="medium" {{ request('size') == 'medium' ? 'selected' : '' }}>{{ __('متوسط') }}</option>
                                <option value="large" {{ request('size') == 'large' ? 'selected' : '' }}>{{ __('كبير') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <button type="submit" class="btn-primary">
                            🔍 {{ __('بحث') }}
                        </button>

                        @if(request()->hasAny(['exhibition_id', 'min_price', 'max_price', 'size']))
                            <a href="{{ route('dashboard.booth-search') }}" class="text-gray-600 hover:text-gray-900">
                                {{ __('مسح المرشحات') }}
                            </a>
                        @endif
                    </div>
                </form>
            </div>

            <!-- Results -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <!-- Results Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">
                            {{ __('الأجنحة المتاحة') }} ({{ $booths->total() }})
                        </h2>

                        <!-- Print Button -->
                        <button onclick="window.print()" class="btn-secondary no-print">
                            🖨️ {{ __('طباعة') }}
                        </button>
                    </div>
                </div>

                <!-- Booths Grid -->
                @if($booths->count() > 0)
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($booths as $booth)
                                <div class="booth-card">
                                    <!-- Booth Header -->
                                    <div class="flex justify-between items-start mb-3">
                                        <div>
                                            <h3 class="font-semibold text-gray-900">{{ __('جناح رقم') }} {{ $booth->booth_number }}</h3>
                                            <p class="text-sm text-gray-600">{{ $booth->exhibition->title ?? __('غير محدد') }}</p>
                                        </div>
                                        <span class="status-badge status-available">
                                            {{ __('متاح') }}
                                        </span>
                                    </div>

                                    <!-- Booth Details -->
                                    <div class="space-y-2 mb-4">
                                        @if($booth->size)
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">{{ __('الحجم') }}:</span>
                                                <span class="font-medium">
                                                    @if($booth->size == 'small') {{ __('صغير') }}
                                                    @elseif($booth->size == 'medium') {{ __('متوسط') }}
                                                    @elseif($booth->size == 'large') {{ __('كبير') }}
                                                    @else {{ $booth->size }}
                                                    @endif
                                                </span>
                                            </div>
                                        @endif

                                        @if($booth->area)
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">{{ __('المساحة') }}:</span>
                                                <span class="font-medium">{{ $booth->area }} {{ __('م²') }}</span>
                                            </div>
                                        @endif

                                        @if($booth->price)
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">{{ __('السعر') }}:</span>
                                                <span class="font-medium" style="color: #10b981;">{{ number_format($booth->price) }} {{ __('د.ك') }}</span>
                                            </div>
                                        @endif

                                        @if($booth->location)
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">{{ __('الموقع') }}:</span>
                                                <span class="font-medium">{{ $booth->location }}</span>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex gap-2">
                                        <a href="/exhibitions/{{ $booth->exhibition_id }}/booths/{{ $booth->id }}"
                                           class="btn-primary flex-1 no-print">
                                            {{ __('عرض التفاصيل') }}
                                        </a>

                                        @auth
                                            <form method="POST" action="/emergency-check-booking/{{ $booth->exhibition_id }}/{{ $booth->id }}" class="flex-1" id="booking-form-{{ $booth->id }}">
                                                @csrf
                                                <button type="submit" class="btn-success w-full no-print">
                                                    {{ __('اختبار الحجز') }}
                                                </button>
                                            </form>

                                            <!-- Simple POST test -->
                                            <form method="POST" action="/test-post-request" class="flex-1 mt-2" style="margin-top: 8px;">
                                                @csrf
                                                <button type="submit" class="btn-secondary w-full no-print" style="background: #6b7280; font-size: 12px; padding: 8px;">
                                                    اختبار POST
                                                </button>
                                            </form>
                                        @else
                                            <a href="/login" class="btn-success flex-1 no-print">
                                                {{ __('سجل دخول للحجز') }}
                                            </a>
                                        @endauth
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        @if($booths->hasPages())
                            <div class="mt-8 no-print">
                                {{ $booths->appends(request()->query())->links() }}
                            </div>
                        @endif
                    </div>
                @else
                    <!-- No Results -->
                    <div class="p-12 text-center">
                        <div class="text-gray-400 text-6xl mb-4">🏢</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('لا توجد أجنحة متاحة') }}</h3>
                        <p class="text-gray-600 mb-4">{{ __('جرب تعديل معايير البحث للعثور على الأجنحة المتاحة') }}</p>

                        @if(request()->hasAny(['exhibition_id', 'min_price', 'max_price', 'size']))
                            <a href="{{ route('dashboard.booth-search') }}" class="btn-primary no-print">
                                {{ __('مسح جميع المرشحات') }}
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Debug Info (only in debug mode) -->
    @if(config('app.debug'))
        <div class="fixed bottom-4 right-4 bg-black text-white p-2 rounded text-xs no-print">
            Version: {{ time() }} | Booths: {{ $booths->total() }}
        </div>
    @endif

    <script>
        // Handle form submission events
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');

            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const button = form.querySelector('button[type="submit"]');

                    // Prevent double submission
                    if (button.disabled) {
                        e.preventDefault();
                        return false;
                    }

                    // Update button state immediately
                    const originalText = button.textContent;
                    button.textContent = 'جاري الإرسال...';
                    button.disabled = true;
                    button.style.backgroundColor = '#6b7280';
                    button.style.cursor = 'not-allowed';

                    // Log for debugging
                    console.log('Form submitted:', form.action);
                    console.log('Form method:', form.method);
                    console.log('Form data:', new FormData(form));

                    // For emergency check routes, handle as AJAX
                    if (form.action.includes('emergency-check-booking') || form.action.includes('test-post-request')) {
                        e.preventDefault();

                        fetch(form.action, {
                            method: 'POST',
                            body: new FormData(form),
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Response:', data);
                            alert('نجح الاختبار! انظر إلى Console للتفاصيل');
                            button.textContent = originalText;
                            button.disabled = false;
                            button.style.backgroundColor = '';
                            button.style.cursor = '';
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('فشل الاختبار! انظر إلى Console للتفاصيل');
                            button.textContent = originalText;
                            button.disabled = false;
                            button.style.backgroundColor = '';
                            button.style.cursor = '';
                        });
                    }
                    // For regular forms, allow normal submission
                });
            });

            // Debug: Log all forms found
            console.log('Found ' + forms.length + ' forms');
        });
    </script>
</body>
</html>
