<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('تفاصيل الحجز') }} #{{ $booking->id }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 9999px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-confirmed {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-cancelled {
            background-color: #fee2e2;
            color: #dc2626;
        }
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 600;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2563eb;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .info-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
        @media print {
            nav, .no-print { display: none !important; }
            body { background: white !important; }
            .info-card { border: 1px solid #ccc !important; box-shadow: none !important; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                        <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                    </a>
                </div>
                
                <div class="flex items-center space-x-4" style="direction: ltr;">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حسابي') }}
                    </a>
                    <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حجوزاتي') }}
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">📋 {{ __('تفاصيل الحجز') }} #{{ $booking->id }}</h1>
                        <p class="mt-2 text-gray-600">{{ __('تاريخ الحجز') }}: {{ $booking->created_at->format('d/m/Y H:i') }}</p>
                    </div>
                    
                    <span class="status-badge status-{{ $booking->status }}">
                        @if($booking->status == 'pending') {{ __('قيد الانتظار') }}
                        @elseif($booking->status == 'confirmed') {{ __('مؤكد') }}
                        @elseif($booking->status == 'cancelled') {{ __('ملغي') }}
                        @else {{ $booking->status }}
                        @endif
                    </span>
                </div>
            </div>

            <!-- Success Message -->
            @if(session('success'))
                <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="text-green-400 text-xl ml-3">✅</div>
                        <div class="text-green-800">{{ session('success') }}</div>
                    </div>
                </div>
            @endif

            <!-- Booking Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Exhibition Info -->
                <div class="info-card">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">🏢 {{ __('معلومات المعرض') }}</h2>
                    <div class="space-y-3">
                        <div>
                            <span class="text-gray-600">{{ __('اسم المعرض') }}:</span>
                            <span class="font-semibold">{{ $booking->exhibition->title ?? __('غير محدد') }}</span>
                        </div>
                        @if($booking->exhibition && $booking->exhibition->start_date)
                            <div>
                                <span class="text-gray-600">{{ __('تاريخ البداية') }}:</span>
                                <span class="font-semibold">{{ $booking->exhibition->start_date->format('d/m/Y') }}</span>
                            </div>
                        @endif
                        @if($booking->exhibition && $booking->exhibition->end_date)
                            <div>
                                <span class="text-gray-600">{{ __('تاريخ النهاية') }}:</span>
                                <span class="font-semibold">{{ $booking->exhibition->end_date->format('d/m/Y') }}</span>
                            </div>
                        @endif
                        @if($booking->exhibition && $booking->exhibition->location)
                            <div>
                                <span class="text-gray-600">{{ __('الموقع') }}:</span>
                                <span class="font-semibold">{{ $booking->exhibition->location }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Booth Info -->
                <div class="info-card">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">🏪 {{ __('معلومات الجناح') }}</h2>
                    <div class="space-y-3">
                        <div>
                            <span class="text-gray-600">{{ __('رقم الجناح') }}:</span>
                            <span class="font-semibold">{{ $booking->booth->booth_number ?? __('غير محدد') }}</span>
                        </div>
                        @if($booking->booth && $booking->booth->size)
                            <div>
                                <span class="text-gray-600">{{ __('الحجم') }}:</span>
                                <span class="font-semibold">
                                    @if($booking->booth->size == 'small') {{ __('صغير') }}
                                    @elseif($booking->booth->size == 'medium') {{ __('متوسط') }}
                                    @elseif($booking->booth->size == 'large') {{ __('كبير') }}
                                    @else {{ $booking->booth->size }}
                                    @endif
                                </span>
                            </div>
                        @endif
                        @if($booking->booth && $booking->booth->area)
                            <div>
                                <span class="text-gray-600">{{ __('المساحة') }}:</span>
                                <span class="font-semibold">{{ $booking->booth->area }} {{ __('م²') }}</span>
                            </div>
                        @endif
                        @if($booking->booth && $booking->booth->location)
                            <div>
                                <span class="text-gray-600">{{ __('موقع الجناح') }}:</span>
                                <span class="font-semibold">{{ $booking->booth->location }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Payment Info -->
            <div class="info-card mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">💳 {{ __('معلومات الدفع') }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <span class="text-gray-600">{{ __('المبلغ الإجمالي') }}:</span>
                        <div class="text-2xl font-bold text-green-600">{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</div>
                    </div>
                    <div>
                        <span class="text-gray-600">{{ __('حالة الدفع') }}:</span>
                        <div class="font-semibold">
                            @if($booking->status == 'confirmed')
                                <span class="text-green-600">{{ __('مدفوع') }}</span>
                            @else
                                <span class="text-yellow-600">{{ __('غير مدفوع') }}</span>
                            @endif
                        </div>
                    </div>
                    <div>
                        <span class="text-gray-600">{{ __('العملة') }}:</span>
                        <div class="font-semibold">{{ $booking->currency ?? 'د.ك' }}</div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-4 justify-center no-print">
                @if($booking->status == 'pending_payment')
                    <a href="/payment/myfatoorah/{{ $booking->id }}" class="btn btn-success">
                        💳 {{ __('إكمال الدفع عبر MyFatoorah') }}
                    </a>
                @endif

                <a href="/dashboard/reservations" class="btn btn-primary">
                    📋 {{ __('جميع الحجوزات') }}
                </a>

                <button onclick="window.print()" class="btn btn-secondary">
                    🖨️ {{ __('طباعة') }}
                </button>

                @if($booking->status == 'confirmed')
                    <a href="/bookings/{{ $booking->id }}/invoice" class="btn btn-secondary">
                        📄 {{ __('تحميل الفاتورة') }}
                    </a>
                @endif
            </div>

            <!-- Next Steps -->
            @if($booking->status == 'pending')
                <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="font-bold text-blue-900 mb-3">📝 {{ __('الخطوات التالية') }}</h3>
                    <ol class="text-blue-800 space-y-2">
                        <li>1. {{ __('إكمال عملية الدفع عبر الرابط أعلاه') }}</li>
                        <li>2. {{ __('انتظار تأكيد الدفع من النظام') }}</li>
                        <li>3. {{ __('استلام تأكيد الحجز عبر البريد الإلكتروني') }}</li>
                        <li>4. {{ __('طباعة الفاتورة والاحتفاظ بها') }}</li>
                    </ol>
                </div>
            @endif
        </div>
    </div>

    <!-- Debug Info (only in debug mode) -->
    @if(config('app.debug'))
        <div class="fixed bottom-4 right-4 bg-black text-white p-2 rounded text-xs no-print">
            Booking ID: {{ $booking->id }} | Status: {{ $booking->status }}
        </div>
    @endif
</body>
</html>
