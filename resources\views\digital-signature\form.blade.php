<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>التوقيع الرقمي - إقرار وتعهد</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .signature-pad {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            background: #f9fafb;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: text;
        }
        .signature-pad:focus-within {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .signature-canvas-container {
            position: relative;
            background: white;
            border-radius: 8px;
        }
        #signatureCanvas {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
            display: block;
            margin: 0 auto;
        }
        #signatureCanvas:hover {
            border-color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <span class="text-gray-700">{{ $user->name }}</span>
                    <a href="/my-bookings" class="text-gray-700 hover:text-blue-600">حجوزاتي</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-8 px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">✍️ التوقيع الرقمي</h1>
            <p class="text-gray-600">إقرار وتعهد قبل إتمام عملية الدفع</p>
        </div>

        <!-- Booking Info -->
        <div class="bg-blue-50 rounded-lg p-6 mb-8">
            <h3 class="font-bold text-blue-900 mb-4">📋 معلومات الحجز</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800">
                <div>
                    <p><strong>المعرض:</strong> {{ $booking->exhibition->title }}</p>
                    <p><strong>الجناح:</strong> {{ $booking->booth->booth_number }}</p>
                </div>
                <div>
                    <p><strong>المبلغ:</strong> {{ number_format($booking->total_amount, 3) }} KWD</p>
                    <p><strong>التاريخ:</strong> {{ $booking->created_at->format('d/m/Y') }}</p>
                </div>
            </div>
        </div>

        <!-- Digital Signature Form -->
        <form method="POST" action="{{ route('booking.signature.store', $booking->id) }}" class="bg-white rounded-lg shadow-lg p-8">
            @csrf

            <!-- Error Messages -->
            @if($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    @foreach($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            @endif

            <!-- Success Messages -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Declaration Text - Official Form -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                <div class="text-center mb-6">
                    <h3 class="font-bold text-yellow-900 text-xl mb-2">إقرار وتعهد</h3>
                    <h4 class="font-semibold text-yellow-800">الشركات المشاركة</h4>
                </div>

                <div class="text-yellow-800 space-y-4 text-sm leading-relaxed text-justify">
                    <p><strong>أتعهد أنا الموقع أدناه</strong></p>
                    <p><strong>المشارك فى معرض {{ $booking->exhibition->title }}</strong></p>
                    <p><strong>المزمع إقامته خلال الفترة</strong> {{ $booking->exhibition->start_date ? $booking->exhibition->start_date->format('d/m/Y') : 'سيتم تحديدها لاحقاً' }} - {{ $booking->exhibition->end_date ? $booking->exhibition->end_date->format('d/m/Y') : 'سيتم تحديدها لاحقاً' }}</p>

                    <p class="leading-relaxed">
                        بأن ألتزم بكل ما تم ذكره في <strong>القرار الوزاري رقم (303) لسنة 2018</strong> بشأن القواعد العامة لتنظيم إقامة المعارض التجارية المؤقتة بدولة الكويت، وأقر بصحة كل ماورد فى كشف السلع والخدمات الخاص بي والمقدم من المنظم، وأتحمل كافة الإجراءات القانونية التي قد تتخذها الوزارة بحالة عدم إلتزامي ببنود هذا التعهد ورد جميع المستحقات إلى أصحابها وتعويضهم التعويض الكامل لتغطية الأضرار التي أصابتهم في حالة ثبوت عدم صحة البيانات والمستندات أو اتضاح وهميتها.
                    </p>

                    <p class="leading-relaxed">
                        كما أتعهد بإعطاء فاتورة في حال الشراء بالمعرض وكذلك الالتزام بمراعاة الآداب العامة وعدم عرض مايخدش بالحياء، وكذلك عدم إقامة عرض أزياء، وألتزم بعدم ذكراسم أي دولة مالم تكن هناك موافقة مسبقة من الوزارة، واتعهد بتسهيل مأمورية المكلفين من الجهات المختصة بالرقابة والإشراف على المعرض.
                    </p>
                </div>
            </div>

            <!-- Company Information Fields -->
            <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
                <h3 class="font-bold text-gray-900 mb-4">📋 بيانات الشركة المشاركة</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="trade_name" class="block text-sm font-medium text-gray-700 mb-2">
                            الاسم التجاري <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="trade_name" name="trade_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               placeholder="اسم الشركة التجاري">
                    </div>

                    <div>
                        <label for="license_holder_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم صاحب الترخيص <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="license_holder_name" name="license_holder_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               placeholder="اسم صاحب الترخيص التجاري">
                    </div>

                    <div>
                        <label for="commercial_license_number" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الترخيص التجاري <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="commercial_license_number" name="commercial_license_number" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               placeholder="رقم الترخيص التجاري">
                    </div>

                    <div>
                        <label for="license_date" class="block text-sm font-medium text-gray-700 mb-2">
                            تاريخه <span class="text-red-500">*</span>
                        </label>
                        <input type="date" id="license_date" name="license_date" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Agreement Checkbox -->
            <div class="space-y-4 mb-8">
                <div class="flex items-start">
                    <input type="checkbox" id="signature_agreement" name="signature_agreement" value="1"
                           class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" required>
                    <label for="signature_agreement" class="mr-3 text-gray-700">
                        <strong>أوافق على الإقرار والتعهد أعلاه</strong> وأتعهد بالالتزام بجميع البنود والشروط المذكورة في القرار الوزاري رقم (303) لسنة 2018
                    </label>
                </div>
            </div>

            <!-- Digital Signature Input -->
            <div class="mb-8">
                <label for="digital_signature" class="block text-sm font-medium text-gray-700 mb-2">
                    ✍️ التوقيع الرقمي (اكتب اسمك الكامل)
                </label>
                <div class="signature-pad">
                    <input type="text" id="digital_signature" name="digital_signature" 
                           placeholder="اكتب اسمك الكامل هنا كتوقيع رقمي..." 
                           class="w-full bg-transparent border-none outline-none text-lg font-semibold text-center"
                           style="font-family: 'Courier New', monospace;" required>
                </div>
                <p class="text-sm text-gray-500 mt-2">
                    💡 اكتب اسمك الكامل كما هو مسجل في الهوية المدنية
                </p>
            </div>

            <!-- User Information Display -->
            <div class="bg-gray-50 rounded-lg p-4 mb-8">
                <h4 class="font-semibold text-gray-900 mb-2">معلومات الموقع:</h4>
                <div class="text-sm text-gray-700 space-y-1">
                    <p><strong>الاسم:</strong> {{ $user->name }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ $user->email }}</p>
                    <p><strong>تاريخ التوقيع:</strong> {{ now()->format('d/m/Y H:i') }}</p>
                </div>
            </div>

            <!-- Digital Signature Canvas -->
            <div class="mb-8">
                <h3 class="font-bold text-gray-900 mb-4">✍️ التوقيع الرقمي</h3>
                <div class="border-2 border-gray-300 rounded-lg p-4">
                    <div class="signature-canvas-container" style="width: 100%; max-width: 600px; margin: 0 auto;">
                        <canvas id="signatureCanvas"
                                width="600"
                                height="200"
                                class="border border-gray-200 rounded cursor-crosshair bg-white"
                                style="touch-action: none; width: 100%; height: 200px; display: block;"></canvas>
                    </div>
                    <div class="mt-3 flex justify-between">
                        <button type="button" id="clearSignature"
                                class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                            🗑️ مسح التوقيع
                        </button>
                        <span class="text-sm text-gray-600">يرجى التوقيع في المربع أعلاه</span>
                    </div>
                </div>
            </div>

            <!-- Hidden field for signature data -->
            <input type="hidden" id="signature" name="signature" required>

            <!-- Submit Buttons -->
            <div class="flex space-x-reverse space-x-4">
                <button type="submit" id="submitBtn" disabled
                        class="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                    ✍️ توقيع الإقرار والمتابعة للدفع
                </button>
                
                <a href="/my-bookings" 
                   class="flex-1 bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors text-center">
                    ← العودة للحجوزات
                </a>
            </div>
        </form>
    </div>

    <script>
        // Digital Signature Canvas Setup
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let hasSignature = false;

        // Set canvas size and properties
        function setupCanvas() {
            // Set actual canvas size
            canvas.width = 600;
            canvas.height = 200;

            // Set drawing properties
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        setupCanvas();

        // Mouse events
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        // Touch events for mobile
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopDrawing);

        function getCanvasCoordinates(e) {
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;

            return {
                x: (e.clientX - rect.left) * scaleX,
                y: (e.clientY - rect.top) * scaleY
            };
        }

        function startDrawing(e) {
            isDrawing = true;
            const coords = getCanvasCoordinates(e);
            ctx.beginPath();
            ctx.moveTo(coords.x, coords.y);
        }

        function draw(e) {
            if (!isDrawing) return;
            const coords = getCanvasCoordinates(e);
            ctx.lineTo(coords.x, coords.y);
            ctx.stroke();
            hasSignature = true;
            updateSignatureData();
            checkFormValidity();
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function handleTouch(e) {
            e.preventDefault();
            if (e.touches && e.touches.length > 0) {
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                                e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }
        }

        // Clear signature
        document.getElementById('clearSignature').addEventListener('click', function() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            hasSignature = false;
            document.getElementById('signature').value = '';
            checkFormValidity();
        });

        // Update signature data
        function updateSignatureData() {
            if (hasSignature) {
                document.getElementById('signature').value = canvas.toDataURL();
            }
        }

        // Enable submit button when all requirements are met
        function checkFormValidity() {
            const agreement = document.getElementById('signature_agreement').checked;
            const tradeNameFilled = document.getElementById('trade_name').value.trim().length > 0;
            const licenseHolderFilled = document.getElementById('license_holder_name').value.trim().length > 0;
            const licenseNumberFilled = document.getElementById('commercial_license_number').value.trim().length > 0;
            const licenseDateFilled = document.getElementById('license_date').value.trim().length > 0;

            const submitBtn = document.getElementById('submitBtn');
            const allValid = agreement && tradeNameFilled && licenseHolderFilled &&
                            licenseNumberFilled && licenseDateFilled && hasSignature;

            submitBtn.disabled = !allValid;
            submitBtn.classList.toggle('bg-green-600', allValid);
            submitBtn.classList.toggle('hover:bg-green-700', allValid);
            submitBtn.classList.toggle('bg-gray-400', !allValid);
        }

        // Add event listeners
        document.getElementById('signature_agreement').addEventListener('change', checkFormValidity);
        document.getElementById('trade_name').addEventListener('input', checkFormValidity);
        document.getElementById('license_holder_name').addEventListener('input', checkFormValidity);
        document.getElementById('commercial_license_number').addEventListener('input', checkFormValidity);
        document.getElementById('license_date').addEventListener('change', checkFormValidity);

        // Initial check
        checkFormValidity();
    </script>
</body>
</html>
