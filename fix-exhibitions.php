<?php
echo "<h1>إصلاح مشكلة المعارض</h1>";

try {
    // Include Laravel autoloader
    require __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    
    echo "<h2>إنشاء معارض تجريبية...</h2>";
    
    // Check if exhibitions already exist
    $existingCount = \App\Models\Exhibition::count();
    echo "عدد المعارض الحالية: " . $existingCount . "<br>";
    
    if ($existingCount == 0) {
        // Create sample exhibitions
        $exhibitions = [
            [
                'title' => 'معرض التكنولوجيا والابتكار 2024',
                'slug' => 'tech-innovation-summit-2024',
                'description' => 'معرض رائد يجمع أحدث التقنيات والابتكارات في مجال التكنولوجيا، يضم أكثر من 200 شركة عارضة من جميع أنحاء العالم.',
                'location' => 'مركز الكويت الدولي للمعارض',
                'venue_name' => 'مركز الكويت الدولي للمعارض',
                'city' => 'الكويت',
                'country' => 'الكويت',
                'start_date' => '2024-03-15',
                'end_date' => '2024-03-18',
                'status' => 'published',
                'organizer_id' => 1,
                'category_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'معرض الصحة والجمال 2024',
                'slug' => 'health-beauty-expo-2024',
                'description' => 'معرض متخصص في منتجات الصحة والجمال، يعرض أحدث المنتجات والخدمات في هذا المجال.',
                'location' => 'مركز الكويت التجاري',
                'venue_name' => 'مركز الكويت التجاري',
                'city' => 'الكويت',
                'country' => 'الكويت',
                'start_date' => '2024-04-10',
                'end_date' => '2024-04-13',
                'status' => 'published',
                'organizer_id' => 1,
                'category_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'معرض السيارات الكويتي 2024',
                'slug' => 'kuwait-auto-show-2024',
                'description' => 'أكبر معرض للسيارات في الكويت، يضم أحدث موديلات السيارات من جميع الماركات العالمية.',
                'location' => 'أرض المعارض الدولية',
                'venue_name' => 'أرض المعارض الدولية',
                'city' => 'الكويت',
                'country' => 'الكويت',
                'start_date' => '2024-05-20',
                'end_date' => '2024-05-25',
                'status' => 'published',
                'organizer_id' => 1,
                'category_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        $createdExhibitions = [];
        foreach ($exhibitions as $exhibitionData) {
            try {
                $exhibition = \App\Models\Exhibition::create($exhibitionData);
                echo "✅ تم إنشاء معرض: " . $exhibition->title . " (ID: " . $exhibition->id . ")<br>";

                // Create sample booths for each exhibition
                $boothCount = 20;
                for ($i = 1; $i <= $boothCount; $i++) {
                    \App\Models\Booth::create([
                        'exhibition_id' => $exhibition->id,
                        'booth_number' => 'A-' . str_pad($i, 2, '0', STR_PAD_LEFT),
                        'size' => 'medium',
                        'area' => rand(20, 50),
                        'price' => rand(400, 800),
                        'status' => $i <= 5 ? 'booked' : 'available',
                        'location' => 'القاعة الرئيسية',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                echo "   ✅ تم إنشاء " . $boothCount . " جناح للمعرض<br>";

                $createdExhibitions[] = $exhibition;
            } catch (Exception $e) {
                echo "❌ خطأ في إنشاء معرض: " . $e->getMessage() . "<br>";
            }
        }

        echo "<h2>✅ تم الانتهاء من إنشاء المعارض!</h2>";
        echo "<h3>روابط الاختبار:</h3>";
        foreach ($createdExhibitions as $exhibition) {
            echo "<a href='/exhibitions/" . $exhibition->id . "/simple' target='_blank'>معرض " . $exhibition->title . " (ID: " . $exhibition->id . ")</a><br>";
            echo "<a href='/exhibitions/" . $exhibition->slug . "/simple' target='_blank'>معرض " . $exhibition->title . " (Slug: " . $exhibition->slug . ")</a><br><br>";
        }
        
    } else {
        echo "<h3>المعارض موجودة بالفعل!</h3>";
        $exhibitions = \App\Models\Exhibition::all();
        echo "<h3>روابط الاختبار:</h3>";
        foreach ($exhibitions as $exhibition) {
            echo "<a href='/exhibitions/" . $exhibition->id . "/simple' target='_blank'>معرض " . $exhibition->title . " (ID: " . $exhibition->id . ")</a><br>";
            echo "<a href='/exhibitions/" . $exhibition->slug . "/simple' target='_blank'>معرض " . $exhibition->title . " (Slug: " . $exhibition->slug . ")</a><br><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في إنشاء المعارض</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
