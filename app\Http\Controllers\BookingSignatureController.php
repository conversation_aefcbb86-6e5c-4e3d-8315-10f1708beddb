<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookingSignatureController extends Controller
{
    /**
     * Show the digital signature form for a booking.
     */
    public function show($bookingId)
    {
        $booking = Booking::with(['exhibition', 'user', 'booth'])
                         ->where('id', $bookingId)
                         ->where('user_id', Auth::id())
                         ->firstOrFail();

        // Allow signature for pending bookings (before payment)
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('error', 'لا يمكن التوقيع على هذا الحجز في حالته الحالية.');
        }

        // Check if already signed
        if ($booking->signature_data) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('info', 'تم التوقيع الرقمي مسبقاً. يمكنك اختيار طريقة الدفع.');
        }

        $user = Auth::user();
        return view('digital-signature.form', compact('booking', 'user'));
    }

    /**
     * Store the digital signature for a booking.
     */
    public function store(Request $request, $bookingId)
    {
        $request->validate([
            'trade_name' => 'required|string|max:255',
            'license_holder_name' => 'required|string|max:255',
            'commercial_license_number' => 'required|string|max:100',
            'license_date' => 'required|date',
            'signature_agreement' => 'required|accepted',
            'signature' => 'required|string',
        ]);

        $booking = Booking::where('id', $bookingId)
                         ->where('user_id', Auth::id())
                         ->firstOrFail();

        // Allow signature for pending bookings (before payment)
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('error', 'لا يمكن التوقيع على هذا الحجز في حالته الحالية.');
        }

        // Store signature data and company information
        $booking->update([
            'signature_data' => $request->signature,
            'signature_date' => now(),
            'signature_ip' => $request->ip(),
            'signature_user_agent' => $request->userAgent(),
            'trade_name' => $request->trade_name,
            'license_holder_name' => $request->license_holder_name,
            'commercial_license_number' => $request->commercial_license_number,
            'license_date' => $request->license_date,
        ]);

        // Redirect back to booking page to show payment options
        return redirect()->route('bookings.show', $booking->id)
                        ->with('success', 'تم حفظ الإقرار والتعهد والتوقيع الرقمي بنجاح. يمكنك الآن اختيار طريقة الدفع.');
    }

    /**
     * View the signed digital signature document.
     */
    public function view($bookingId)
    {
        $booking = Booking::with(['exhibition', 'user', 'booth'])
                         ->where('id', $bookingId)
                         ->firstOrFail();

        // Check if user has permission to view this signature
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';

        if (!$isAdmin && $booking->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بعرض هذا المستند');
        }

        // Check if signature exists
        if (!$booking->signature_data) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('error', 'لا يوجد إقرار وتعهد موقع لهذا الحجز.');
        }

        return view('digital-signature.view', compact('booking'));
    }

    /**
     * Admin page to view all signatures.
     */
    public function adminIndex(Request $request)
    {
        // Check if user is admin
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';

        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        // Build query
        $query = Booking::with(['exhibition', 'user', 'booth'])
                       ->whereNotNull('signature_data');

        // Apply filters
        if ($request->exhibition) {
            $query->where('exhibition_id', $request->exhibition);
        }

        if ($request->payment_status === 'paid') {
            $query->where('status', 'confirmed');
        } elseif ($request->payment_status === 'pending') {
            $query->where('status', 'pending');
        }

        if ($request->from_date) {
            $query->whereDate('signature_date', '>=', $request->from_date);
        }

        // Get bookings with pagination
        $bookings = $query->orderBy('signature_date', 'desc')->paginate(20);

        // Statistics
        $totalSignatures = Booking::whereNotNull('signature_data')->count();
        $todaySignatures = Booking::whereNotNull('signature_data')
                                 ->whereDate('signature_date', today())
                                 ->count();
        $weekSignatures = Booking::whereNotNull('signature_data')
                                ->whereBetween('signature_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->count();
        $paidSignatures = Booking::whereNotNull('signature_data')
                                ->where('status', 'confirmed')
                                ->count();

        // Get exhibitions for filter
        $exhibitions = \App\Models\Exhibition::orderBy('title')->get();

        return view('admin.signatures', compact(
            'bookings',
            'totalSignatures',
            'todaySignatures',
            'weekSignatures',
            'paidSignatures',
            'exhibitions'
        ));
    }
}
