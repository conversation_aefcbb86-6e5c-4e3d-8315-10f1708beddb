<template>
  <Link 
    :href="href" 
    :class="linkClass"
    class="hover:opacity-80 transition-opacity"
  >
    <img 
      :src="logoSrc" 
      :alt="altText" 
      :class="imageClass"
      class="w-auto"
    >
  </Link>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
  // Logo variant: 'default', 'white', 'small'
  variant: {
    type: String,
    default: 'default'
  },
  // Size: 'xs', 'sm', 'md', 'lg', 'xl'
  size: {
    type: String,
    default: 'md'
  },
  // Link destination
  href: {
    type: String,
    default: '/'
  },
  // Alt text
  altText: {
    type: String,
    default: 'Season Expo'
  },
  // Additional CSS classes for the link
  linkClass: {
    type: String,
    default: ''
  },
  // Additional CSS classes for the image
  imageClass: {
    type: String,
    default: ''
  }
});

// Compute logo source based on variant
const logoSrc = computed(() => {
  switch (props.variant) {
    case 'white':
      return '/images/logo-white.png';
    case 'small':
      return '/images/logo-small.png';
    default:
      return '/images/logo.png';
  }
});

// Compute size classes
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'xs':
      return 'h-6';
    case 'sm':
      return 'h-8';
    case 'md':
      return 'h-10';
    case 'lg':
      return 'h-12';
    case 'xl':
      return 'h-16';
    case '2xl':
      return 'h-20';
    default:
      return 'h-10';
  }
});

// Combine image classes
const finalImageClass = computed(() => {
  return `${sizeClasses.value} ${props.imageClass}`;
});
</script>

<style scoped>
/* Print styles for logo */
@media print {
  .logo-link {
    display: inline-block !important;
  }
  
  .logo-image {
    max-height: 60px !important;
    width: auto !important;
  }
}
</style>
