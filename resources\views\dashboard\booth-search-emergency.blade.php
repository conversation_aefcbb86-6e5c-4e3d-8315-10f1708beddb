<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>البحث عن الأجنحة - Season Expo Kuwait</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav {
            background: #2563eb;
            color: white;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav a:hover {
            background: rgba(255,255,255,0.2);
        }
        .search-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .btn {
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn-success {
            background: #10b981;
        }
        .btn-success:hover {
            background: #059669;
        }
        .results {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .results-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .booth-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .booth-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }
        .booth-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-color: #2563eb;
        }
        .booth-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 10px;
        }
        .booth-title {
            font-weight: bold;
            color: #333;
        }
        .booth-subtitle {
            color: #666;
            font-size: 14px;
        }
        .status-badge {
            background: #d1fae5;
            color: #065f46;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .booth-details {
            margin: 15px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .detail-label {
            color: #666;
        }
        .detail-value {
            font-weight: bold;
            color: #333;
        }
        .price {
            color: #10b981;
            font-weight: bold;
        }
        .booth-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .booth-actions a {
            flex: 1;
            text-align: center;
            padding: 8px 12px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
        }
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .no-results-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .error-message {
            background: #fee2e2;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #fecaca;
        }
        @media print {
            .nav, .search-form, .no-print { display: none !important; }
            body { background: white !important; }
        }
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            .booth-grid {
                grid-template-columns: 1fr;
            }
            .results-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav no-print">
        <div class="container">
            <a href="/">🏠 الرئيسية</a>
            <a href="/dashboard">📊 حسابي</a>
            <a href="/dashboard/reservations">📋 حجوزاتي</a>
            <a href="/exhibitions">🏢 المعارض</a>
        </div>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔍 البحث عن الأجنحة</h1>
            <p>ابحث عن الجناح المثالي لمعرضك</p>
        </div>

        <!-- Error Message (if any) -->
        @if(isset($error))
            <div class="error-message">
                <strong>تنبيه:</strong> حدثت مشكلة في تحميل البيانات. يتم عرض النسخة المبسطة من الصفحة.
                <br><small>تفاصيل الخطأ: {{ $error }}</small>
            </div>
        @endif

        <!-- Search Form -->
        <div class="search-form no-print">
            <form method="GET" action="/dashboard/booth-search">
                <div class="form-row">
                    <div class="form-group">
                        <label for="exhibition_id">المعرض</label>
                        <select name="exhibition_id" id="exhibition_id">
                            <option value="">جميع المعارض</option>
                            <!-- Static options for emergency -->
                            <option value="1">Tech Innovation Summit 2024</option>
                            <option value="2">Kuwait Business Expo</option>
                            <option value="3">Health & Wellness Fair</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="min_price">السعر الأدنى (د.ك)</label>
                        <input type="number" name="min_price" id="min_price" placeholder="0" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="max_price">السعر الأعلى (د.ك)</label>
                        <input type="number" name="max_price" id="max_price" placeholder="10000" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="size">الحجم</label>
                        <select name="size" id="size">
                            <option value="">جميع الأحجام</option>
                            <option value="small">صغير</option>
                            <option value="medium">متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <button type="submit" class="btn">🔍 بحث</button>
                    <a href="/dashboard/booth-search" class="btn" style="background: #6b7280;">🔄 إعادة تعيين</a>
                </div>
            </form>
        </div>

        <!-- Results -->
        <div class="results">
            <div class="results-header">
                <h2>الأجنحة المتاحة</h2>
                <button onclick="window.print()" class="btn no-print">🖨️ طباعة</button>
            </div>

            <!-- Sample Booths (Static for Emergency) -->
            <div class="booth-grid">
                <!-- Booth 1 -->
                <div class="booth-card">
                    <div class="booth-header">
                        <div>
                            <div class="booth-title">جناح رقم A-101</div>
                            <div class="booth-subtitle">Tech Innovation Summit 2024</div>
                        </div>
                        <span class="status-badge">متاح</span>
                    </div>
                    
                    <div class="booth-details">
                        <div class="detail-row">
                            <span class="detail-label">الحجم:</span>
                            <span class="detail-value">متوسط</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المساحة:</span>
                            <span class="detail-value">25 م²</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">السعر:</span>
                            <span class="detail-value price">1,500 د.ك</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الموقع:</span>
                            <span class="detail-value">القاعة الرئيسية</span>
                        </div>
                    </div>
                    
                    <div class="booth-actions">
                        <a href="/exhibitions/1/booths/101" class="btn">عرض التفاصيل</a>
                        <a href="/exhibitions/1/booths/101/book" class="btn btn-success">احجز الآن</a>
                    </div>
                </div>

                <!-- Booth 2 -->
                <div class="booth-card">
                    <div class="booth-header">
                        <div>
                            <div class="booth-title">جناح رقم B-205</div>
                            <div class="booth-subtitle">Kuwait Business Expo</div>
                        </div>
                        <span class="status-badge">متاح</span>
                    </div>
                    
                    <div class="booth-details">
                        <div class="detail-row">
                            <span class="detail-label">الحجم:</span>
                            <span class="detail-value">كبير</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المساحة:</span>
                            <span class="detail-value">50 م²</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">السعر:</span>
                            <span class="detail-value price">2,800 د.ك</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الموقع:</span>
                            <span class="detail-value">القاعة الثانية</span>
                        </div>
                    </div>
                    
                    <div class="booth-actions">
                        <a href="/exhibitions/2/booths/205" class="btn">عرض التفاصيل</a>
                        <a href="/exhibitions/2/booths/205/book" class="btn btn-success">احجز الآن</a>
                    </div>
                </div>

                <!-- Booth 3 -->
                <div class="booth-card">
                    <div class="booth-header">
                        <div>
                            <div class="booth-title">جناح رقم C-150</div>
                            <div class="booth-subtitle">Health & Wellness Fair</div>
                        </div>
                        <span class="status-badge">متاح</span>
                    </div>
                    
                    <div class="booth-details">
                        <div class="detail-row">
                            <span class="detail-label">الحجم:</span>
                            <span class="detail-value">صغير</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المساحة:</span>
                            <span class="detail-value">15 م²</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">السعر:</span>
                            <span class="detail-value price">950 د.ك</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الموقع:</span>
                            <span class="detail-value">القاعة الثالثة</span>
                        </div>
                    </div>
                    
                    <div class="booth-actions">
                        <a href="/exhibitions/3/booths/150" class="btn">عرض التفاصيل</a>
                        <a href="/exhibitions/3/booths/150/book" class="btn btn-success">احجز الآن</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alternative Links -->
        <div style="margin-top: 30px; text-align: center; padding: 20px; background: white; border-radius: 8px;">
            <h3>روابط بديلة</h3>
            <div style="margin-top: 15px;">
                <a href="/booth-search-v2" class="btn">🔄 النسخة البديلة</a>
                <a href="/dashboard/booth-search-force-fix" class="btn">🛠️ إصلاح قسري</a>
                <a href="/debug-booth-search" class="btn">🔍 تشخيص المشكلة</a>
            </div>
        </div>
    </div>

    <script>
        // Simple form enhancement
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when filters change
            const form = document.querySelector('form');
            const selects = form.querySelectorAll('select');
            
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    // Optional: auto-submit on change
                    // form.submit();
                });
            });
            
            // Add loading state to search button
            form.addEventListener('submit', function() {
                const btn = form.querySelector('button[type="submit"]');
                btn.innerHTML = '⏳ جاري البحث...';
                btn.disabled = true;
            });
        });
    </script>
</body>
</html>
