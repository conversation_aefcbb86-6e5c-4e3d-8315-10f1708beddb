Stack trace:
Frame         Function      Args
0007FFFFB770  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB770, 0007FFFFA670) msys-2.0.dll+0x1FEBA
0007FFFFB770  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA48) msys-2.0.dll+0x67F9
0007FFFFB770  000210046832 (000210285FF9, 0007FFFFB628, 0007FFFFB770, 000000000000) msys-2.0.dll+0x6832
0007FFFFB770  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB770  0002100690B4 (0007FFFFB780, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA50  00021006A49D (0007FFFFB780, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE643A0000 ntdll.dll
7FFE63750000 KERNEL32.DLL
7FFE61CA0000 KERNELBASE.dll
7FFE63410000 USER32.dll
7FFE618B0000 win32u.dll
7FFE63C70000 GDI32.dll
7FFE61B80000 gdi32full.dll
7FFE62030000 msvcp_win.dll
7FFE620D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE62570000 advapi32.dll
7FFE63930000 msvcrt.dll
7FFE63B20000 sechost.dll
7FFE639F0000 RPCRT4.dll
7FFE610A0000 CRYPTBASE.DLL
7FFE61830000 bcryptPrimitives.dll
7FFE63890000 IMM32.DLL
