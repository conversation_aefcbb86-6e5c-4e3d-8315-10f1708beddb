<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('تفاصيل المستخدم') }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        body {
            font-family: {{ app()->getLocale() == 'ar' ? "'Noto Sans Arabic', sans-serif" : "'Figtree', sans-serif" }};
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="/" class="flex-shrink-0">
                            <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                            <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <a href="{{ route('admin.users.index') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            {{ __('العودة للمستخدمين') }}
                        </a>
                        <a href="/admin" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            {{ __('لوحة التحكم') }}
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <!-- Header -->
                        <div class="flex justify-between items-center mb-6">
                            <h1 class="text-2xl font-bold text-gray-900">{{ __('تفاصيل المستخدم') }}</h1>
                            <div class="flex space-x-2 rtl:space-x-reverse">
                                <a href="{{ route('admin.users.edit', $user->id) }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                    {{ __('تعديل') }}
                                </a>
                                <a href="{{ route('admin.users.index') }}"
                                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                    {{ __('العودة') }}
                                </a>
                            </div>
                        </div>

                        <!-- User Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <!-- Basic Info -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ __('المعلومات الأساسية') }}</h2>
                                
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('الاسم') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->name }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('البريد الإلكتروني') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->email }}</p>
                                    </div>
                                    
                                    @if($user->phone)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('رقم الهاتف') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->phone }}</p>
                                    </div>
                                    @endif
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('الدور') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">
                                            @if($user->role === 'admin')
                                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">{{ __('مدير') }}</span>
                                            @else
                                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">{{ __('مستخدم') }}</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Status -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ __('حالة الحساب') }}</h2>
                                
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('تاريخ التسجيل') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('Y-m-d H:i') }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('آخر تحديث') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->updated_at->format('Y-m-d H:i') }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('حالة التفعيل') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">
                                            @if($user->email_verified_at)
                                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">{{ __('مفعل') }}</span>
                                            @else
                                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">{{ __('غير مفعل') }}</span>
                                            @endif
                                        </p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">{{ __('عدد الحجوزات') }}</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->bookings_count }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bookings -->
                        @if($user->bookings && $user->bookings->count() > 0)
                        <div class="bg-white border border-gray-200 rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-lg font-semibold text-gray-900">{{ __('الحجوزات') }} ({{ $user->bookings->count() }})</h2>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('المعرض') }}</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('الجناح') }}</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('الحالة') }}</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('تاريخ الحجز') }}</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('الإجراءات') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($user->bookings as $booking)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $booking->exhibition->title ?? __('غير محدد') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $booking->booth->booth_number ?? __('غير محدد') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($booking->status === 'confirmed')
                                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">{{ __('مؤكد') }}</span>
                                                @elseif($booking->status === 'pending')
                                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">{{ __('في الانتظار') }}</span>
                                                @else
                                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">{{ $booking->status }}</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $booking->created_at->format('Y-m-d') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="/bookings/{{ $booking->id }}" class="text-blue-600 hover:text-blue-900">{{ __('عرض') }}</a>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        @else
                        <div class="bg-gray-50 rounded-lg p-6 text-center">
                            <p class="text-gray-500">{{ __('لا توجد حجوزات لهذا المستخدم') }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
