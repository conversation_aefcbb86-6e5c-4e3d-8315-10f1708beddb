<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>التوقيع الإلكتروني - Season Expo Kuwait</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .signature-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 600;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            width: 100%;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                        <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                    </a>
                </div>
                
                <div class="flex items-center space-x-4" style="direction: ltr;">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        حسابي
                    </a>
                    <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        حجوزاتي
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-12">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">✍️ التوقيع الإلكتروني</h1>
                <p class="mt-2 text-gray-600">إقرار وتعهد الشركات المشاركة</p>
                <p class="text-sm text-blue-600">حجز رقم #{{ $booking->id }}</p>
            </div>

            <!-- Booking Summary -->
            <div class="signature-card mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">📋 ملخص الحجز</h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">المعرض:</span>
                        <span class="font-semibold">{{ $booking->exhibition->title ?? 'غير محدد' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">رقم الجناح:</span>
                        <span class="font-semibold">{{ $booking->booth->booth_number ?? 'غير محدد' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">المبلغ:</span>
                        <span class="font-semibold">{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</span>
                    </div>
                </div>
            </div>

            <!-- Digital Signature Form -->
            <div class="signature-card">
                <h2 class="text-xl font-bold text-gray-900 mb-4">📝 إقرار وتعهد</h2>
                
                <form method="POST" action="/digital-signature/{{ $booking->id }}" id="signatureForm">
                    @csrf
                    
                    <div class="space-y-6">
                        <!-- Trade Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم التجاري *</label>
                            <input type="text" name="trade_name" class="form-input" required 
                                   value="{{ old('trade_name', Auth::user()->company ?? '') }}"
                                   placeholder="أدخل الاسم التجاري للشركة">
                        </div>

                        <!-- License Holder Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم صاحب الرخصة *</label>
                            <input type="text" name="license_holder_name" class="form-input" required 
                                   value="{{ old('license_holder_name', Auth::user()->name) }}"
                                   placeholder="أدخل اسم صاحب الرخصة التجارية">
                        </div>

                        <!-- Commercial License Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الرخصة التجارية *</label>
                            <input type="text" name="commercial_license_number" class="form-input" required 
                                   value="{{ old('commercial_license_number') }}"
                                   placeholder="أدخل رقم الرخصة التجارية">
                        </div>

                        <!-- License Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الرخصة *</label>
                            <input type="date" name="license_date" class="form-input" required 
                                   value="{{ old('license_date') }}">
                        </div>

                        <!-- Declaration Text -->
                        <div class="bg-white border-2 border-black p-8" style="font-family: 'Times New Roman', serif;">
                            <div class="text-center mb-8">
                                <h2 class="text-2xl font-bold mb-4">إقرار وتعهد</h2>
                                <h3 class="text-xl font-bold">الشركات المشاركة</h3>
                            </div>

                            <div class="text-right leading-relaxed space-y-4" style="font-size: 16px; line-height: 2;">
                                <p class="mb-6">
                                    أتعهد أنا الموقع أدناه ................................................................<br>
                                    المشارك في معرض ................................................................<br>
                                    المزمع إقامته خلال الفترة ................................................................
                                </p>

                                <p class="text-justify leading-loose">
                                    بأن ألتزم بكل ما تم ذكره في القرار الوزاري رقم (303) لسنة 2018 بشأن القواعد العامة
                                    لتنظيم إقامة المعارض التجارية المؤقتة بدولة الكويت، وأقر بصحة كل ما ورد في كشف البيانات
                                    والخدمات الخاص بي والمقدم من المنظم، وأتحمل كافة الإجراءات القانونية التي قد تتخذها
                                    الوزارة بحالة عدم التزامي بهذا التعهد ورد جميع المستندات إلى أصحابها وتعويضهم
                                    التعويض الكامل أتعهد في حالة ثبوت عدم صحة الأضرار التي لحقت بهم في حالة ثبوت عدم صحة البيانات
                                    والمستندات أو أتضاح أو هميتها.
                                </p>

                                <p class="text-justify leading-loose">
                                    كما أتعهد بإعطاء فترة في حال الشراء بالمعرض وكذلك الالتزام بمراعاة الآداب العامة
                                    وعدم عرض مايخدش بالحياء، وكذلك عدم إقامة عرض أزياء، والتزم بعدم دخولي أي دولة
                                    مالم تكن هناك موافقة مسبقة من الوزارة، وأتعهد بتسهيل مأمورية المكلفين من الجهات
                                    المختصة بالرقابة والإشراف على المعرض.
                                </p>

                                <div class="mt-12 space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span>الاسم التجاري:</span>
                                        <div class="border-b border-black w-64"></div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>اسم صاحب الترخيص:</span>
                                        <div class="border-b border-black w-64"></div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>رقم الترخيص التجاري:</span>
                                        <div class="border-b border-black w-64"></div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>تاريخ:</span>
                                        <div class="border-b border-black w-64"></div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>التوقيع:</span>
                                        <div class="border-b border-black w-64"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Digital Signature Canvas -->
                        <div class="bg-gray-50 border border-gray-300 rounded-lg p-6">
                            <h3 class="font-bold text-gray-900 mb-4">✍️ التوقيع الإلكتروني</h3>
                            <p class="text-sm text-gray-600 mb-4">يرجى التوقيع في المربع أدناه باستخدام الماوس أو اللمس:</p>

                            <div class="bg-white border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4">
                                <canvas id="signatureCanvas" width="600" height="200"
                                        class="border border-gray-200 rounded cursor-crosshair w-full"
                                        style="touch-action: none; max-width: 100%; height: auto;">
                                    متصفحك لا يدعم التوقيع الإلكتروني
                                </canvas>
                            </div>

                            <div class="flex gap-3 mb-4">
                                <button type="button" id="clearSignature"
                                        class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors text-sm">
                                    🗑️ مسح التوقيع
                                </button>
                                <button type="button" id="undoSignature"
                                        class="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors text-sm">
                                    ↶ تراجع
                                </button>
                            </div>

                            <input type="hidden" name="signature_data" id="signatureData" required>
                            <div id="signatureError" class="text-red-600 text-sm hidden">
                                يرجى إضافة التوقيع قبل المتابعة
                            </div>
                        </div>

                        <!-- Agreement Checkbox -->
                        <div class="flex items-start">
                            <input type="checkbox" name="signature_agreement" id="signature_agreement"
                                   class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded" required>
                            <label for="signature_agreement" class="mr-3 text-sm text-gray-700">
                                أوافق على الإقرار والتعهد المذكور أعلاه وأؤكد صحة جميع البيانات المقدمة *
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="pt-4">
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                ✍️ توقيع إلكتروني والمتابعة للدفع
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Info -->
            <div class="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 class="font-bold text-green-900 mb-3">ℹ️ معلومات مهمة</h3>
                <ul class="text-green-800 space-y-2 text-sm">
                    <li>• التوقيع الإلكتروني إجباري لإتمام عملية الحجز</li>
                    <li>• بعد التوقيع سيتم تحويلك لبوابة الدفع الآمنة</li>
                    <li>• جميع البيانات محفوظة ومشفرة</li>
                    <li>• يمكنك طباعة الإقرار بعد إتمام الحجز</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Digital Signature Canvas Setup
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let strokes = [];
        let currentStroke = [];

        // Set canvas size
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * 2;
            canvas.height = rect.height * 2;
            ctx.scale(2, 2);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Mouse events
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        // Touch events
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopDrawing);

        function startDrawing(e) {
            isDrawing = true;
            currentStroke = [];
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) * (canvas.width / rect.width) / 2;
            const y = (e.clientY - rect.top) * (canvas.height / rect.height) / 2;
            currentStroke.push({x, y});
            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function draw(e) {
            if (!isDrawing) return;
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) * (canvas.width / rect.width) / 2;
            const y = (e.clientY - rect.top) * (canvas.height / rect.height) / 2;
            currentStroke.push({x, y});
            ctx.lineTo(x, y);
            ctx.stroke();
        }

        function stopDrawing() {
            if (isDrawing) {
                isDrawing = false;
                strokes.push([...currentStroke]);
                updateSignatureData();
            }
        }

        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }

        function updateSignatureData() {
            const dataURL = canvas.toDataURL();
            document.getElementById('signatureData').value = dataURL;
            document.getElementById('signatureError').classList.add('hidden');
        }

        // Clear signature
        document.getElementById('clearSignature').addEventListener('click', function() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            strokes = [];
            document.getElementById('signatureData').value = '';
        });

        // Undo last stroke
        document.getElementById('undoSignature').addEventListener('click', function() {
            if (strokes.length > 0) {
                strokes.pop();
                redrawCanvas();
            }
        });

        function redrawCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            strokes.forEach(stroke => {
                if (stroke.length > 0) {
                    ctx.beginPath();
                    ctx.moveTo(stroke[0].x, stroke[0].y);
                    stroke.forEach(point => {
                        ctx.lineTo(point.x, point.y);
                    });
                    ctx.stroke();
                }
            });
            updateSignatureData();
        }

        // Form submission
        document.getElementById('signatureForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const signatureData = document.getElementById('signatureData').value;

            // Check if signature exists
            if (!signatureData || signatureData === '') {
                e.preventDefault();
                document.getElementById('signatureError').classList.remove('hidden');
                canvas.scrollIntoView({ behavior: 'smooth' });
                return false;
            }

            // Prevent double submission
            if (submitBtn.disabled) {
                e.preventDefault();
                return false;
            }

            // Update button state
            submitBtn.textContent = 'جاري المعالجة...';
            submitBtn.disabled = true;
            submitBtn.style.backgroundColor = '#6b7280';
        });
    </script>
</body>
</html>
