<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Exhibition;
use App\Models\Booth;
use App\Models\Payment;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{
    /**
     * Display a listing of user's bookings.
     */
    public function index(Request $request): Response
    {
        $user = auth()->user();

        $query = $user->bookings()->with(['exhibition', 'booth', 'payments']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(10);

        return Inertia::render('Bookings/Index', [
            'bookings' => $bookings,
            'filters' => $request->only(['status']),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Display the specified booking.
     */
    public function show(Booking $booking)
    {
        // Ensure user can only view their own bookings
        if ($booking->user_id !== auth()->id()) {
            abort(403);
        }

        $booking->load(['exhibition', 'booth', 'payments']);

        // Use simple HTML view instead of Inertia to avoid Vite issues
        return view('bookings.show-simple', [
            'booking' => $booking,
        ]);
    }

    /**
     * Book a booth.
     */
    public function book(Request $request, Exhibition $exhibition, Booth $booth): RedirectResponse
    {
        // Ensure user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('message', 'Please login to book a booth.');
        }

        // Ensure booth belongs to exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        // Ensure booth is available
        if (!$booth->isAvailable()) {
            return back()->withErrors(['booth' => 'This booth is no longer available.']);
        }

        // Ensure exhibition registration is open
        if (!$exhibition->isRegistrationOpen()) {
            return back()->withErrors(['exhibition' => 'Registration for this exhibition is closed.']);
        }

        try {
            DB::beginTransaction();

            // Create booking
            $booking = Booking::create([
                'user_id' => auth()->id(),
                'exhibition_id' => $exhibition->id,
                'booth_id' => $booth->id,
                'status' => 'pending',
                'total_amount' => $booth->price,
                'currency' => $exhibition->currency,
                'booking_date' => now(),
                'exhibitor_details' => [
                    'company' => auth()->user()->company ?? '',
                    'contact_person' => auth()->user()->name,
                    'email' => auth()->user()->email,
                    'phone' => auth()->user()->phone ?? '',
                ],
            ]);

            // Reserve the booth temporarily
            $booth->update(['status' => 'reserved']);

            // Create pending payment
            Payment::create([
                'booking_id' => $booking->id,
                'user_id' => auth()->id(),
                'amount' => $booth->price,
                'currency' => $exhibition->currency,
                'status' => 'pending',
                'payment_method' => 'credit_card',
            ]);

            DB::commit();

            // Redirect to payment check (which will check signature first)
            return redirect("/payment/check/{$booking->id}")
                ->with('success', 'تم حجز الجناح! يرجى إكمال الإقرار والتعهد والدفع.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['booking' => 'Failed to create booking. Please try again.']);
        }
    }

    /**
     * Show the booking form.
     */
    public function create(Request $request): Response
    {
        $exhibition = Exhibition::findOrFail($request->get('exhibition_id'));
        $booth = Booth::findOrFail($request->get('booth_id'));

        // Ensure booth belongs to exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        // Ensure booth is available
        if (!$booth->isAvailable()) {
            return redirect()->route('booths.show', [$exhibition, $booth])
                ->withErrors(['booth' => 'This booth is no longer available.']);
        }

        return Inertia::render('Bookings/Create', [
            'exhibition' => $exhibition->load('category', 'organizer'),
            'booth' => $booth,
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Store a newly created booking.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'exhibition_id' => 'required|exists:exhibitions,id',
            'booth_id' => 'required|exists:booths,id',
            'exhibitor_details' => 'required|array',
            'exhibitor_details.company' => 'required|string|max:255',
            'exhibitor_details.contact_person' => 'required|string|max:255',
            'exhibitor_details.email' => 'required|email',
            'exhibitor_details.phone' => 'required|string|max:20',
            'special_requirements' => 'nullable|string',
            'additional_services' => 'nullable|array',
        ]);

        $exhibition = Exhibition::findOrFail($validated['exhibition_id']);
        $booth = Booth::findOrFail($validated['booth_id']);

        // Ensure booth belongs to exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        // Ensure booth is available
        if (!$booth->isAvailable()) {
            return back()->withErrors(['booth' => 'This booth is no longer available.']);
        }

        // Ensure exhibition registration is open
        if (!$exhibition->isRegistrationOpen()) {
            return back()->withErrors(['exhibition' => 'Registration for this exhibition is closed.']);
        }

        try {
            DB::beginTransaction();

            // Calculate total amount (booth price + additional services)
            $totalAmount = $booth->price;
            if (!empty($validated['additional_services'])) {
                // Add logic for additional services pricing
                foreach ($validated['additional_services'] as $service) {
                    // This would be based on your service pricing logic
                    $totalAmount += 0; // Placeholder
                }
            }

            // Create booking
            $booking = Booking::create([
                'user_id' => auth()->id(),
                'exhibition_id' => $exhibition->id,
                'booth_id' => $booth->id,
                'status' => 'pending',
                'total_amount' => $totalAmount,
                'currency' => $exhibition->currency,
                'booking_date' => now(),
                'exhibitor_details' => $validated['exhibitor_details'],
                'additional_services' => $validated['additional_services'] ?? [],
                'special_requirements' => $validated['special_requirements'],
            ]);

            // Reserve the booth temporarily
            $booth->update(['status' => 'reserved']);

            // Create pending payment
            Payment::create([
                'booking_id' => $booking->id,
                'user_id' => auth()->id(),
                'amount' => $totalAmount,
                'currency' => $exhibition->currency,
                'status' => 'pending',
                'payment_method' => 'credit_card',
            ]);

            DB::commit();

            return redirect("/payment/check/{$booking->id}")
                ->with('success', 'تم إنشاء الحجز بنجاح! يرجى إكمال الإقرار والتعهد والدفع.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['booking' => 'Failed to create booking. Please try again.']);
        }
    }

    /**
     * Cancel a booking.
     */
    public function destroy(Booking $booking): RedirectResponse
    {
        // Ensure user can only cancel their own bookings
        if ($booking->user_id !== auth()->id()) {
            abort(403);
        }

        // Only allow cancellation of pending bookings
        if ($booking->status !== 'pending') {
            return back()->withErrors(['booking' => 'Only pending bookings can be cancelled.']);
        }

        try {
            DB::beginTransaction();

            // Cancel the booking
            $booking->cancel('Cancelled by user');

            // Cancel any pending payments
            $booking->payments()->where('status', 'pending')->update([
                'status' => 'failed',
                'failed_at' => now(),
                'failure_reason' => 'Booking cancelled by user',
            ]);

            DB::commit();

            return redirect()->route('bookings.index')
                ->with('success', 'Booking cancelled successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['booking' => 'Failed to cancel booking. Please try again.']);
        }
    }
}
