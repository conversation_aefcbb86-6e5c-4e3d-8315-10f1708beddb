<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('إكمال الدفع') }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
        }
        .payment-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 600;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            width: 100%;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .amount-display {
            font-size: 2rem;
            font-weight: bold;
            color: #10b981;
            text-align: center;
            margin: 20px 0;
        }
        .payment-method {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.2s;
        }
        .payment-method:hover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .payment-method.selected {
            border-color: #10b981;
            background-color: #d1fae5;
        }
        .security-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
            padding: 12px;
            border-radius: 8px;
            margin-top: 16px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                        <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                    </a>
                </div>
                
                <div class="flex items-center space-x-4" style="direction: ltr;">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حسابي') }}
                    </a>
                    <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حجوزاتي') }}
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-12">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">💳 {{ __('إكمال الدفع') }}</h1>
                <p class="mt-2 text-gray-600">{{ __('حجز رقم') }} #{{ $booking->id }}</p>
            </div>

            <!-- Payment Summary -->
            <div class="payment-card mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">📋 {{ __('ملخص الحجز') }}</h2>
                
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between">
                        <span class="text-gray-600">{{ __('المعرض') }}:</span>
                        <span class="font-semibold">{{ $booking->exhibition->title ?? __('غير محدد') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">{{ __('رقم الجناح') }}:</span>
                        <span class="font-semibold">{{ $booking->booth->booth_number ?? __('غير محدد') }}</span>
                    </div>
                    @if($booking->booth && $booking->booth->area)
                        <div class="flex justify-between">
                            <span class="text-gray-600">{{ __('المساحة') }}:</span>
                            <span class="font-semibold">{{ $booking->booth->area }} {{ __('م²') }}</span>
                        </div>
                    @endif
                </div>

                <hr class="my-4">
                
                <div class="amount-display">
                    {{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="payment-card mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">💳 {{ __('طريقة الدفع') }}</h2>
                
                <div class="space-y-3">
                    <!-- K-Net -->
                    <div class="payment-method selected" onclick="selectPaymentMethod(this)">
                        <div class="flex items-center">
                            <div class="text-2xl ml-3">🏦</div>
                            <div>
                                <div class="font-semibold">K-Net</div>
                                <div class="text-sm text-gray-600">{{ __('الدفع عبر البطاقة البنكية الكويتية') }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Credit Card -->
                    <div class="payment-method" onclick="selectPaymentMethod(this)">
                        <div class="flex items-center">
                            <div class="text-2xl ml-3">💳</div>
                            <div>
                                <div class="font-semibold">{{ __('بطاقة ائتمان') }}</div>
                                <div class="text-sm text-gray-600">Visa, MasterCard</div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Payment -->
                    <div class="payment-method" onclick="selectPaymentMethod(this)">
                        <div class="flex items-center">
                            <div class="text-2xl ml-3">📱</div>
                            <div>
                                <div class="font-semibold">{{ __('الدفع الإلكتروني') }}</div>
                                <div class="text-sm text-gray-600">{{ __('محفظة إلكترونية') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Actions -->
            <div class="payment-card">
                <div class="space-y-4">
                    <!-- Main Payment Button -->
                    <form method="POST" action="/payment/complete/{{ $booking->id }}">
                        @csrf
                        <button type="submit" class="btn btn-success" id="paymentBtn"
                                onclick="this.textContent='جاري المعالجة...'; this.disabled=true; this.form.submit();">
                            🔒 {{ __('تأكيد الدفع') }}
                        </button>
                    </form>

                    <!-- Alternative: MyFatoorah Integration -->
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-2">{{ __('أو استخدم بوابة الدفع المتقدمة') }}</p>
                        <a href="/payment/myfatoorah/{{ $booking->id }}" class="btn btn-secondary">
                            🌐 {{ __('الدفع عبر MyFatoorah') }}
                        </a>
                    </div>

                    <!-- Back Button -->
                    <div class="text-center pt-4">
                        <a href="/bookings/{{ $booking->id }}" class="text-gray-600 hover:text-gray-900">
                            ← {{ __('العودة لتفاصيل الحجز') }}
                        </a>
                    </div>
                </div>

                <!-- Security Badge -->
                <div class="security-badge">
                    <div class="text-green-600 text-lg ml-2">🔒</div>
                    <div class="text-sm text-gray-700">
                        {{ __('عملية دفع آمنة ومشفرة') }}
                    </div>
                </div>
            </div>

            <!-- Payment Info -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="font-bold text-blue-900 mb-3">ℹ️ {{ __('معلومات مهمة') }}</h3>
                <ul class="text-blue-800 space-y-2 text-sm">
                    <li>• {{ __('سيتم تأكيد حجزك فور إتمام عملية الدفع') }}</li>
                    <li>• {{ __('ستصلك رسالة تأكيد عبر البريد الإلكتروني') }}</li>
                    <li>• {{ __('يمكنك طباعة الفاتورة من صفحة تفاصيل الحجز') }}</li>
                    <li>• {{ __('في حالة وجود مشاكل، تواصل مع الدعم الفني') }}</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function selectPaymentMethod(element) {
            // Remove selected class from all payment methods
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('selected');
            });
            
            // Add selected class to clicked method
            element.classList.add('selected');
        }

        // Auto-submit form after delay (for demo purposes)
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const paymentBtn = document.getElementById('paymentBtn');
            
            form.addEventListener('submit', function(e) {
                // Add loading state
                paymentBtn.innerHTML = '⏳ جاري المعالجة...';
                paymentBtn.disabled = true;
                
                // For demo: auto-submit after 2 seconds
                setTimeout(() => {
                    // Form will submit normally
                }, 2000);
            });
        });
    </script>
</body>
</html>
