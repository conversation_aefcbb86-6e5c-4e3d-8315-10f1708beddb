<?php
echo "<h1>فحص المعارض في قاعدة البيانات</h1>";

try {
    // Include Laravel autoloader
    require __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    
    echo "<h2>حالة قاعدة البيانات:</h2>";
    
    // Check if exhibitions table exists
    $exhibitions = \App\Models\Exhibition::all();
    echo "✅ عدد المعارض الموجودة: " . $exhibitions->count() . "<br>";
    
    if ($exhibitions->count() > 0) {
        echo "<h3>المعارض الموجودة:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>العنوان</th><th>Slug</th><th>الحالة</th><th>تاريخ البداية</th></tr>";
        
        foreach ($exhibitions as $exhibition) {
            echo "<tr>";
            echo "<td>" . $exhibition->id . "</td>";
            echo "<td>" . $exhibition->title . "</td>";
            echo "<td>" . $exhibition->slug . "</td>";
            echo "<td>" . $exhibition->status . "</td>";
            echo "<td>" . $exhibition->start_date . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>روابط الاختبار:</h3>";
        foreach ($exhibitions as $exhibition) {
            echo "<a href='/exhibitions/" . $exhibition->id . "/simple' target='_blank'>معرض " . $exhibition->title . " (ID)</a><br>";
            echo "<a href='/exhibitions/" . $exhibition->slug . "/simple' target='_blank'>معرض " . $exhibition->title . " (Slug)</a><br><br>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ لا توجد معارض في قاعدة البيانات</h3>";
        echo "<p>تحتاج لإنشاء معارض تجريبية أولاً</p>";
        echo "<a href='/create-simple-exhibitions' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>إنشاء معارض تجريبية</a>";
        echo "</div>";
    }
    
    // Check booths
    $booths = \App\Models\Booth::all();
    echo "<h3>الأجنحة:</h3>";
    echo "✅ عدد الأجنحة الموجودة: " . $booths->count() . "<br>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p><strong>الحل:</strong> تأكد من أن قاعدة البيانات تعمل وأن ملف .env محدث</p>";
    echo "</div>";
}
?>
