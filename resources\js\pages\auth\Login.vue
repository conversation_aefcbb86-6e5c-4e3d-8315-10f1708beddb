<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';

const props = defineProps({
    status: String,
    canResetPassword: Boolean,
    locale: {
        type: String,
        default: 'ar'
    }
});

// Translation helper
const t = (key, fallback) => {
    const translations = {
        // Page title
        'sign_in': 'تسجيل الدخول',
        'season_expo': 'Season Expo',

        // Navigation
        'register': 'إنشاء حساب',

        // Header
        'welcome_back': 'مرحباً بعودتك',
        'sign_in_subtitle': 'سجل دخولك لإدارة المعارض والحجوزات',

        // Left panel
        'welcome_to_hub': 'مرحباً بك في مركز المعارض',
        'dashboard_access': 'الوصول للوحة التحكم',
        'dashboard_desc': 'عرض حجوزاتك وإدارة المعارض وتتبع نمو عملك',
        'booth_management': 'إدارة الأجنحة',
        'booth_desc': 'احجز أجنحة جديدة وعدل الحجوزات الحالية وأدر حضورك في المعارض',
        'network_connect': 'التواصل والشبكات',
        'network_desc': 'تواصل مع العارضين والمنظمين والمهنيين في الصناعة',
        'secure_platform': 'منصة آمنة وموثوقة',
        'security_desc': 'بياناتك محمية بأمان على مستوى المؤسسات',

        // Form labels
        'email_address': 'عنوان البريد الإلكتروني',
        'email_placeholder': 'أدخل عنوان بريدك الإلكتروني',
        'password': 'كلمة المرور',
        'password_placeholder': 'أدخل كلمة المرور',
        'forgot_password': 'نسيت كلمة المرور؟',
        'remember_me': 'تذكرني لمدة 30 يوماً',

        // Buttons
        'signing_in': 'جاري تسجيل الدخول...',
        'sign_in_account': 'تسجيل الدخول إلى حسابك',

        // Register link
        'no_account': 'ليس لديك حساب؟',
        'create_now': 'أنشئ واحداً الآن',

        // Demo accounts
        'demo_accounts': 'حسابات تجريبية (للاختبار):',
        'exhibitor': 'عارض',
        'organizer': 'منظم',
        'admin': 'مدير',

        // Footer
        'secure_login': 'تسجيل دخول آمن بواسطة Season Expo',
        'back_homepage': 'العودة للصفحة الرئيسية'
    };

    return props.locale === 'ar' ? (translations[key] || fallback) : fallback;
};

const isRTL = props.locale === 'ar';

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.post('/login', {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
  <Head :title="t('sign_in', 'تسجيل الدخول')" />

  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 pt-24" :dir="isRTL ? 'rtl' : 'ltr'">
    <div class="max-w-4xl w-full space-y-8">
      <!-- Fixed Navigation -->
      <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <Link href="/" class="hover:opacity-80 transition-opacity">
                <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
              </Link>
            </div>
            <div :class="isRTL ? 'flex items-center space-x-reverse space-x-4' : 'flex items-center space-x-4'">
              <Link href="/register" class="text-gray-600 hover:text-gray-900">{{ t('register', 'Register') }}</Link>
            </div>
          </div>
        </div>
      </nav>

      <!-- Header -->
      <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ t('welcome_back', 'Welcome Back') }}</h2>
        <p class="text-gray-600">{{ t('sign_in_subtitle', 'Sign in to your account to manage exhibitions and bookings') }}</p>
      </div>

      <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2">
          <!-- Left Side - Welcome Message -->
          <div class="bg-gradient-to-br from-blue-600 to-purple-700 p-8 text-white">
            <h3 class="text-2xl font-bold mb-6">{{ t('welcome_to_hub', 'Welcome to Your Exhibition Hub') }}</h3>
            <div class="space-y-6">
              <div :class="isRTL ? 'flex items-start space-x-reverse space-x-3' : 'flex items-start space-x-3'">
                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span class="text-lg">📊</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ t('dashboard_access', 'Dashboard Access') }}</h4>
                  <p class="text-blue-100 text-sm">{{ t('dashboard_desc', 'View your bookings, manage exhibitions, and track your business growth') }}</p>
                </div>
              </div>
              <div :class="isRTL ? 'flex items-start space-x-reverse space-x-3' : 'flex items-start space-x-3'">
                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span class="text-lg">🎯</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ t('booth_management', 'Booth Management') }}</h4>
                  <p class="text-blue-100 text-sm">{{ t('booth_desc', 'Book new booths, modify existing reservations, and manage your exhibition presence') }}</p>
                </div>
              </div>
              <div :class="isRTL ? 'flex items-start space-x-reverse space-x-3' : 'flex items-start space-x-3'">
                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span class="text-lg">🤝</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ t('network_connect', 'Network & Connect') }}</h4>
                  <p class="text-blue-100 text-sm">{{ t('network_desc', 'Connect with other exhibitors, organizers, and industry professionals') }}</p>
                </div>
              </div>
            </div>

            <div class="mt-8 p-4 bg-white bg-opacity-10 rounded-lg">
              <p class="text-sm text-blue-100">
                <span class="font-semibold">{{ t('secure_platform', 'Secure & Trusted Platform') }}</span><br>
                {{ t('security_desc', 'Your data is protected with enterprise-grade security') }}
              </p>
            </div>
          </div>

          <!-- Right Side - Login Form -->
          <div class="p-8">
            <!-- Status Message -->
            <div v-if="status" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p class="text-sm text-green-600 text-center">{{ status }}</p>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
              <!-- Email -->
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">{{ t('email_address', 'Email Address') }}</label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  required
                  autofocus
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  :placeholder="t('email_placeholder', 'Enter your email address')"
                  :dir="isRTL ? 'rtl' : 'ltr'"
                />
                <div v-if="form.errors.email" class="mt-2 text-sm text-red-600">{{ form.errors.email }}</div>
              </div>

              <!-- Password -->
              <div>
                <div :class="isRTL ? 'flex flex-row-reverse items-center justify-between mb-2' : 'flex items-center justify-between mb-2'">
                  <label for="password" class="block text-sm font-medium text-gray-700">{{ t('password', 'Password') }}</label>
                  <Link
                    v-if="canResetPassword"
                    href="/forgot-password"
                    class="text-sm text-blue-600 hover:text-blue-500"
                  >
                    {{ t('forgot_password', 'Forgot password?') }}
                  </Link>
                </div>
                <input
                  id="password"
                  v-model="form.password"
                  type="password"
                  required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  :placeholder="t('password_placeholder', 'Enter your password')"
                  :dir="isRTL ? 'rtl' : 'ltr'"
                />
                <div v-if="form.errors.password" class="mt-2 text-sm text-red-600">{{ form.errors.password }}</div>
              </div>

              <!-- Remember Me -->
              <div :class="isRTL ? 'flex flex-row-reverse items-center' : 'flex items-center'">
                <input
                  id="remember"
                  v-model="form.remember"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="remember" :class="isRTL ? 'mr-2 block text-sm text-gray-700' : 'ml-2 block text-sm text-gray-700'">
                  {{ t('remember_me', 'Remember me for 30 days') }}
                </label>
              </div>

              <!-- Submit Button -->
              <button
                type="submit"
                :disabled="form.processing"
                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                <span v-if="form.processing" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ t('signing_in', 'Signing In...') }}
                </span>
                <span v-else>{{ t('sign_in_account', 'Sign In to Your Account') }}</span>
              </button>

              <!-- Register Link -->
              <div class="text-center">
                <p class="text-sm text-gray-600">
                  {{ t('no_account', "Don't have an account?") }}
                  <Link href="/register" class="font-medium text-blue-600 hover:text-blue-500">
                    {{ t('create_now', 'Create one now') }}
                  </Link>
                </p>
              </div>

              <!-- Demo Accounts -->
              <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-2">{{ t('demo_accounts', 'Demo Accounts (for testing):') }}</h4>
                <div class="text-xs text-gray-600 space-y-1">
                  <p><strong>{{ t('exhibitor', 'Exhibitor') }}:</strong> <EMAIL> / password</p>
                  <p><strong>{{ t('organizer', 'Organizer') }}:</strong> <EMAIL> / password</p>
                  <p><strong>{{ t('admin', 'Admin') }}:</strong> <EMAIL> / password</p>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center text-sm text-gray-500">
        <div class="flex justify-center items-center space-x-2 space-x-reverse">
          <img src="/images/logo-small.png" alt="Season Expo" class="h-4 w-auto">
          <span>•</span>
          <Link href="/" class="text-blue-600 hover:text-blue-500">{{ t('back_homepage', 'العودة للصفحة الرئيسية') }}</Link>
        </div>
      </div>
    </div>
  </div>
</template>
