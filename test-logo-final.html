<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الشعار النهائي - منصة المعارض</title>
    
    <!-- Print Styles -->
    <link rel="stylesheet" href="/css/print-styles.css" media="print">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .logo-demo {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 10px 0;
            border: 2px dashed #ccc;
            border-radius: 8px;
        }
        .success { border-color: #10b981; background-color: #f0fdf4; }
        .error { border-color: #ef4444; background-color: #fef2f2; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background-color: #10b981; }
        .status-error { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="bg-white shadow-sm mb-8 no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <button onclick="runAllTests()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        🧪 تشغيل جميع الاختبارات
                    </button>
                    <button onclick="window.print()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                        🖨️ اختبار الطباعة
                    </button>
                    <a href="/" class="text-gray-600 hover:text-gray-900">العودة للرئيسية</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">اختبار الشعار النهائي</h1>
        
        <!-- Test Results Summary -->
        <div id="testResults" class="test-section mb-8">
            <h2 class="text-xl font-bold mb-4">نتائج الاختبارات</h2>
            <div id="testSummary" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-600" id="totalTests">0</div>
                    <div class="text-sm text-gray-500">إجمالي الاختبارات</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="passedTests">0</div>
                    <div class="text-sm text-green-500">اختبارات ناجحة</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="failedTests">0</div>
                    <div class="text-sm text-red-500">اختبارات فاشلة</div>
                </div>
            </div>
        </div>

        <!-- Logo Variants Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">اختبار أنواع الشعار</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="logo-demo" id="logoDefault">
                    <div class="text-center">
                        <img src="/images/logo.png" alt="Default Logo" class="h-16 w-auto mx-auto mb-2">
                        <p class="text-sm">الشعار الافتراضي</p>
                        <span class="status-indicator" id="statusDefault"></span>
                    </div>
                </div>
                <div class="logo-demo bg-gray-800" id="logoWhite">
                    <div class="text-center">
                        <img src="/images/logo-white.png" alt="White Logo" class="h-16 w-auto mx-auto mb-2">
                        <p class="text-sm text-white">الشعار الأبيض</p>
                        <span class="status-indicator" id="statusWhite"></span>
                    </div>
                </div>
                <div class="logo-demo" id="logoSmall">
                    <div class="text-center">
                        <img src="/images/logo-small.png" alt="Small Logo" class="h-8 w-auto mx-auto mb-2">
                        <p class="text-sm">الشعار الصغير</p>
                        <span class="status-indicator" id="statusSmall"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">اختبار شريط التنقل</h2>
            <div class="logo-demo" id="navTest">
                <nav class="bg-white shadow-sm w-full">
                    <div class="max-w-7xl mx-auto px-4">
                        <div class="flex justify-between h-16">
                            <div class="flex items-center">
                                <a href="/" class="hover:opacity-80 transition-opacity">
                                    <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                                </a>
                            </div>
                            <div class="flex items-center space-x-reverse space-x-4">
                                <a href="#" class="text-gray-600 hover:text-gray-900">المعارض</a>
                                <a href="#" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                            </div>
                        </div>
                    </div>
                </nav>
                <span class="status-indicator mt-2" id="statusNav"></span>
            </div>
        </div>

        <!-- Print Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">اختبار الطباعة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">رأس الصفحة عند الطباعة</h3>
                    <div class="border p-4 bg-gray-50 text-center">
                        <img src="/images/logo.png" alt="Print Header" class="h-12 w-auto mx-auto mb-2">
                        <div class="text-sm font-bold">منصة المعارض</div>
                        <div class="text-xs text-gray-600">منصة المعارض الموسمية الرائدة</div>
                    </div>
                    <span class="status-indicator mt-2" id="statusPrintHeader"></span>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">تذييل الصفحة عند الطباعة</h3>
                    <div class="border p-4 bg-gray-50 text-center">
                        <img src="/images/logo-small.png" alt="Print Footer" class="h-6 w-auto mx-auto mb-2">
                        <div class="text-xs text-gray-600">© 2024 جميع الحقوق محفوظة</div>
                    </div>
                    <span class="status-indicator mt-2" id="statusPrintFooter"></span>
                </div>
            </div>
        </div>

        <!-- File Existence Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">اختبار وجود الملفات</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">ملفات الشعار</h3>
                    <ul class="space-y-2" id="logoFiles">
                        <!-- Will be populated by JavaScript -->
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">ملفات النظام</h3>
                    <ul class="space-y-2" id="systemFiles">
                        <!-- Will be populated by JavaScript -->
                    </ul>
                </div>
            </div>
        </div>

        <!-- Performance Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">اختبار الأداء</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="loadTime">--</div>
                    <div class="text-sm text-blue-500">وقت التحميل (ms)</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="fileSize">--</div>
                    <div class="text-sm text-purple-500">حجم الملفات (KB)</div>
                </div>
                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600" id="renderTime">--</div>
                    <div class="text-sm text-orange-500">وقت العرض (ms)</div>
                </div>
            </div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h2 class="text-xl font-bold mb-4">سجل الاختبارات</h2>
            <div id="testLog" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
                <!-- Test log will appear here -->
            </div>
        </div>
    </div>

    <!-- Logo Configuration -->
    <script src="/js/logo-config.js"></script>
    
    <!-- Print Helper Script -->
    <script src="/js/print-helper.js"></script>
    
    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            tests: []
        };

        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-green-400',
                success: 'text-green-300',
                error: 'text-red-400',
                warning: 'text-yellow-400'
            };
            
            logElement.innerHTML += `<div class="${colors[type]}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, success) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status-indicator ${success ? 'status-success' : 'status-error'}`;
            }
        }

        function updateTestSummary() {
            document.getElementById('totalTests').textContent = testResults.total;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
        }

        async function testImageExists(url, name) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => {
                    log(`✅ ${name}: تم تحميل الصورة بنجاح`, 'success');
                    resolve(true);
                };
                img.onerror = () => {
                    log(`❌ ${name}: فشل في تحميل الصورة`, 'error');
                    resolve(false);
                };
                img.src = url;
            });
        }

        async function testLogoVariants() {
            log('بدء اختبار أنواع الشعار...', 'info');
            
            const logos = [
                { url: '/images/logo.png', name: 'الشعار الافتراضي', statusId: 'statusDefault' },
                { url: '/images/logo-white.png', name: 'الشعار الأبيض', statusId: 'statusWhite' },
                { url: '/images/logo-small.png', name: 'الشعار الصغير', statusId: 'statusSmall' }
            ];

            for (const logo of logos) {
                testResults.total++;
                const success = await testImageExists(logo.url, logo.name);
                updateStatus(logo.statusId, success);
                
                if (success) {
                    testResults.passed++;
                } else {
                    testResults.failed++;
                }
            }
        }

        async function testSystemFiles() {
            log('بدء اختبار ملفات النظام...', 'info');
            
            const files = [
                { url: '/css/print-styles.css', name: 'ملف أنماط الطباعة' },
                { url: '/js/print-helper.js', name: 'مساعد الطباعة' },
                { url: '/js/logo-config.js', name: 'إعدادات الشعار' }
            ];

            const systemFilesElement = document.getElementById('systemFiles');
            
            for (const file of files) {
                testResults.total++;
                
                try {
                    const response = await fetch(file.url, { method: 'HEAD' });
                    const success = response.ok;
                    
                    const li = document.createElement('li');
                    li.innerHTML = `
                        <span class="status-indicator ${success ? 'status-success' : 'status-error'}"></span>
                        ${file.name}
                    `;
                    systemFilesElement.appendChild(li);
                    
                    if (success) {
                        testResults.passed++;
                        log(`✅ ${file.name}: موجود`, 'success');
                    } else {
                        testResults.failed++;
                        log(`❌ ${file.name}: غير موجود`, 'error');
                    }
                } catch (error) {
                    testResults.failed++;
                    log(`❌ ${file.name}: خطأ في الاختبار`, 'error');
                }
            }
        }

        async function testPerformance() {
            log('بدء اختبار الأداء...', 'info');
            
            const startTime = performance.now();
            
            // Test logo loading time
            await testImageExists('/images/logo.png', 'اختبار الأداء');
            
            const endTime = performance.now();
            const loadTime = Math.round(endTime - startTime);
            
            document.getElementById('loadTime').textContent = loadTime;
            document.getElementById('renderTime').textContent = Math.round(performance.now());
            
            log(`⏱️ وقت التحميل: ${loadTime}ms`, 'info');
        }

        async function runAllTests() {
            log('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            testResults = { total: 0, passed: 0, failed: 0, tests: [] };
            
            await testLogoVariants();
            await testSystemFiles();
            await testPerformance();
            
            updateTestSummary();
            
            const successRate = Math.round((testResults.passed / testResults.total) * 100);
            
            if (successRate >= 90) {
                log(`🎉 جميع الاختبارات مكتملة! معدل النجاح: ${successRate}%`, 'success');
            } else if (successRate >= 70) {
                log(`⚠️ الاختبارات مكتملة مع تحذيرات. معدل النجاح: ${successRate}%`, 'warning');
            } else {
                log(`❌ فشل في عدة اختبارات. معدل النجاح: ${successRate}%`, 'error');
            }
        }

        // Run tests automatically when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة الاختبار', 'info');
            setTimeout(runAllTests, 1000);
        });

        // Test print functionality
        window.addEventListener('beforeprint', function() {
            log('🖨️ بدء عملية الطباعة...', 'info');
        });

        window.addEventListener('afterprint', function() {
            log('✅ انتهت عملية الطباعة', 'success');
        });
    </script>
</body>
</html>
