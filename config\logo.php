<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Logo Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the platform logo and branding.
    | You can customize the logo paths, sizes, and other branding elements.
    |
    */

    'default' => [
        'path' => '/images/logo.png',
        'alt' => 'Season Expo',
        'width' => null,
        'height' => 40, // Default height in pixels
    ],

    'white' => [
        'path' => '/images/logo-white.png',
        'alt' => 'Season Expo',
        'width' => null,
        'height' => 40,
    ],

    'small' => [
        'path' => '/images/logo-small.png',
        'alt' => 'Season Expo',
        'width' => null,
        'height' => 24,
    ],

    'favicon' => [
        'path' => '/images/favicon.png',
        'sizes' => '32x32',
        'type' => 'image/png',
    ],

    'apple_touch_icon' => [
        'path' => '/images/apple-touch-icon.png',
        'sizes' => '180x180',
    ],

    'android_chrome' => [
        '192' => '/images/android-chrome-192x192.png',
        '512' => '/images/android-chrome-512x512.png',
    ],

    /*
    |--------------------------------------------------------------------------
    | Platform Branding
    |--------------------------------------------------------------------------
    */

    'platform' => [
        'name' => 'منصة المعارض',
        'name_en' => 'Season Expo',
        'tagline' => 'منصة المعارض الموسمية الرائدة',
        'tagline_en' => 'Leading Seasonal Exhibition Platform',
        'url' => env('APP_URL', 'http://localhost'),
        'copyright' => 'جميع الحقوق محفوظة',
        'copyright_en' => 'All Rights Reserved',
    ],

    /*
    |--------------------------------------------------------------------------
    | Print Settings
    |--------------------------------------------------------------------------
    */

    'print' => [
        'header' => [
            'show_logo' => true,
            'logo_height' => 60,
            'show_title' => true,
            'show_url' => true,
            'show_date' => true,
        ],
        'footer' => [
            'show_logo' => true,
            'logo_height' => 30,
            'show_copyright' => true,
            'show_url' => true,
        ],
        'styles' => [
            'font_family' => 'Arial, sans-serif',
            'font_size' => '12pt',
            'line_height' => '1.4',
            'margin' => '20px',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media
    |--------------------------------------------------------------------------
    */

    'social' => [
        'og_image' => '/images/og-image.png',
        'twitter_image' => '/images/twitter-image.png',
        'linkedin_image' => '/images/linkedin-image.png',
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Templates
    |--------------------------------------------------------------------------
    */

    'email' => [
        'header_logo' => '/images/logo.png',
        'footer_logo' => '/images/logo-small.png',
        'max_width' => 600,
        'logo_height' => 50,
    ],

    /*
    |--------------------------------------------------------------------------
    | PDF Documents
    |--------------------------------------------------------------------------
    */

    'pdf' => [
        'header_logo' => '/images/logo.png',
        'footer_logo' => '/images/logo-small.png',
        'watermark' => '/images/logo-watermark.png',
        'header_height' => 60,
        'footer_height' => 30,
        'watermark_opacity' => 0.1,
    ],

    /*
    |--------------------------------------------------------------------------
    | Mobile App
    |--------------------------------------------------------------------------
    */

    'mobile' => [
        'splash_screen' => '/images/splash-screen.png',
        'app_icon' => '/images/app-icon.png',
        'notification_icon' => '/images/notification-icon.png',
    ],

    /*
    |--------------------------------------------------------------------------
    | Loading and Placeholder
    |--------------------------------------------------------------------------
    */

    'placeholder' => [
        'loading' => '/images/loading-logo.gif',
        'error' => '/images/error-logo.png',
        'no_image' => '/images/no-image.png',
    ],
];
