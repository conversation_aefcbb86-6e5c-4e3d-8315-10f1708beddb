/**
 * Print Helper for Season Expo Platform
 * Automatically adds logo and branding to printed documents
 */

class PrintHelper {
    constructor() {
        this.logoUrl = '/images/logo.png';
        this.logoWhiteUrl = '/images/logo-white.png';
        this.platformName = 'منصة المعارض';
        this.websiteUrl = window.location.origin;
        
        this.init();
    }
    
    init() {
        // Add print styles if not already present
        this.addPrintStyles();
        
        // Listen for print events
        this.setupPrintListeners();
        
        // Add print button if needed
        this.addPrintButton();
    }
    
    addPrintStyles() {
        // Check if print styles are already loaded
        if (document.querySelector('link[href*="print-styles.css"]')) {
            return;
        }
        
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = '/css/print-styles.css';
        link.media = 'print';
        document.head.appendChild(link);
    }
    
    setupPrintListeners() {
        // Before print event
        window.addEventListener('beforeprint', () => {
            this.preparePrintDocument();
        });
        
        // After print event
        window.addEventListener('afterprint', () => {
            this.cleanupPrintDocument();
        });
    }
    
    preparePrintDocument() {
        // Add print header if not exists
        this.addPrintHeader();
        
        // Add print footer if not exists
        this.addPrintFooter();
        
        // Add print date
        this.addPrintDate();
        
        // Hide unnecessary elements
        this.hideNonPrintElements();
    }
    
    cleanupPrintDocument() {
        // Remove temporary print elements
        const printElements = document.querySelectorAll('.temp-print-element');
        printElements.forEach(element => element.remove());
        
        // Show hidden elements
        this.showNonPrintElements();
    }
    
    addPrintHeader() {
        // Check if header already exists
        if (document.querySelector('.print-header')) {
            return;
        }
        
        const header = document.createElement('div');
        header.className = 'print-header print-only temp-print-element';
        header.innerHTML = `
            <img src="${this.logoUrl}" alt="Season Expo" class="print-logo">
            <div class="print-title">${this.platformName}</div>
            <div class="print-subtitle">منصة المعارض الموسمية الرائدة</div>
            <div class="print-subtitle">${this.websiteUrl}</div>
        `;
        
        // Insert at the beginning of body
        document.body.insertBefore(header, document.body.firstChild);
    }
    
    addPrintFooter() {
        // Check if footer already exists
        if (document.querySelector('.print-footer')) {
            return;
        }
        
        const footer = document.createElement('div');
        footer.className = 'print-footer print-only temp-print-element';
        footer.innerHTML = `
            <img src="${this.logoUrl}" alt="Season Expo" class="print-footer-logo">
            <div>© ${new Date().getFullYear()} جميع الحقوق محفوظة</div>
            <div>${this.websiteUrl}</div>
        `;
        
        // Insert at the end of body
        document.body.appendChild(footer);
    }
    
    addPrintDate() {
        // Check if date already exists
        if (document.querySelector('.print-date')) {
            return;
        }
        
        const now = new Date();
        const dateStr = now.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const dateDiv = document.createElement('div');
        dateDiv.className = 'print-date print-only temp-print-element';
        dateDiv.innerHTML = `تاريخ الطباعة: ${dateStr}`;
        
        // Insert after header
        const header = document.querySelector('.print-header');
        if (header) {
            header.insertAdjacentElement('afterend', dateDiv);
        } else {
            document.body.insertBefore(dateDiv, document.body.firstChild);
        }
    }
    
    hideNonPrintElements() {
        // Store original display values
        this.hiddenElements = [];
        
        const selectorsToHide = [
            'nav:not(.print-nav)',
            '.navbar',
            '.sidebar',
            '.menu',
            'button:not(.print-button)',
            '.btn:not(.print-btn)',
            '.no-print',
            '.screen-only',
            '.fixed:not(.print-fixed)',
            '.sticky:not(.print-sticky)',
            '.modal',
            '.popup',
            '.tooltip',
            '.dropdown'
        ];
        
        selectorsToHide.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element.style.display !== 'none') {
                    this.hiddenElements.push({
                        element: element,
                        originalDisplay: element.style.display || 'block'
                    });
                    element.style.display = 'none';
                }
            });
        });
    }
    
    showNonPrintElements() {
        // Restore original display values
        if (this.hiddenElements) {
            this.hiddenElements.forEach(item => {
                item.element.style.display = item.originalDisplay;
            });
            this.hiddenElements = [];
        }
    }
    
    addPrintButton() {
        // Check if print button already exists
        if (document.querySelector('.print-button')) {
            return;
        }
        
        const printButton = document.createElement('button');
        printButton.className = 'print-button screen-only';
        printButton.innerHTML = '🖨️ طباعة';
        printButton.onclick = () => this.print();
        
        document.body.appendChild(printButton);
    }
    
    print() {
        window.print();
    }
    
    // Method to print specific content
    printContent(contentSelector) {
        const content = document.querySelector(contentSelector);
        if (!content) {
            console.error('Content not found:', contentSelector);
            return;
        }
        
        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>طباعة - ${this.platformName}</title>
                <link rel="stylesheet" href="/css/print-styles.css">
                <style>
                    body { margin: 0; padding: 20px; }
                    .print-header, .print-footer { display: block !important; }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <img src="${this.logoUrl}" alt="Season Expo" class="print-logo">
                    <div class="print-title">${this.platformName}</div>
                    <div class="print-subtitle">منصة المعارض الموسمية الرائدة</div>
                    <div class="print-subtitle">${this.websiteUrl}</div>
                </div>
                
                <div class="print-date">
                    تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    })}
                </div>
                
                <div class="print-content">
                    ${content.innerHTML}
                </div>
                
                <div class="print-footer">
                    <img src="${this.logoUrl}" alt="Season Expo" class="print-footer-logo">
                    <div>© ${new Date().getFullYear()} جميع الحقوق محفوظة</div>
                    <div>${this.websiteUrl}</div>
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        
        // Wait for images to load then print
        printWindow.onload = () => {
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
    }
    
    // Method to generate PDF-ready content
    generatePDFContent(contentSelector) {
        const content = document.querySelector(contentSelector);
        if (!content) {
            console.error('Content not found:', contentSelector);
            return null;
        }
        
        return `
            <div class="pdf-document" style="font-family: Arial, sans-serif; direction: rtl;">
                <div class="pdf-header" style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
                    <img src="${this.logoUrl}" alt="Season Expo" style="max-height: 60px; margin-bottom: 10px;">
                    <h1 style="margin: 10px 0; color: #333;">${this.platformName}</h1>
                    <p style="color: #666; margin: 5px 0;">منصة المعارض الموسمية الرائدة</p>
                    <p style="color: #666; margin: 5px 0;">${this.websiteUrl}</p>
                </div>
                
                <div class="pdf-date" style="text-align: left; font-size: 10pt; color: #666; margin-bottom: 20px;">
                    تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    })}
                </div>
                
                <div class="pdf-content">
                    ${content.innerHTML}
                </div>
                
                <div class="pdf-footer" style="text-align: center; margin-top: 30px; border-top: 1px solid #ccc; padding-top: 20px; font-size: 10pt; color: #666;">
                    <img src="${this.logoUrl}" alt="Season Expo" style="max-height: 30px; margin-bottom: 10px;">
                    <p>© ${new Date().getFullYear()} جميع الحقوق محفوظة</p>
                    <p>${this.websiteUrl}</p>
                </div>
            </div>
        `;
    }
}

// Initialize PrintHelper when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.printHelper = new PrintHelper();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrintHelper;
}
