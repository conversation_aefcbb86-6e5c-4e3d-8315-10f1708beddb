<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminUserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        // Check if user is admin
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        // Build query
        $query = User::withCount('bookings');

        // Apply filters
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->role) {
            $query->where('role', $request->role);
        }

        if ($request->from_date) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        // Get users with pagination
        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        // Statistics
        $totalUsers = User::count();
        $activeUsers = User::whereNotNull('email_verified_at')->count();
        $todayUsers = User::whereDate('created_at', today())->count();
        $adminUsers = User::where('role', 'admin')->count();

        return view('admin.users', compact(
            'users', 
            'totalUsers', 
            'activeUsers', 
            'todayUsers', 
            'adminUsers'
        ));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        // Check if user is admin
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        return view('admin.users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        // Check if user is admin
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,exhibitor,organizer',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['email_verified_at'] = now();

        User::create($validated);

        return redirect()->route('admin.users.index')
                        ->with('success', 'تم إنشاء المستخدم بنجاح');
    }

    /**
     * Display the specified user.
     */
    public function show($id)
    {
        // Check if user is admin
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $user = User::with(['bookings.exhibition', 'bookings.booth'])
                   ->withCount('bookings')
                   ->findOrFail($id);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit($id)
    {
        // Check if user is admin
        $user = Auth::user();
        $isAdmin = $user->email === '<EMAIL>' || $user->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $user = User::findOrFail($id);

        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, $id)
    {
        // Check if user is admin
        $currentUser = Auth::user();
        $isAdmin = $currentUser->email === '<EMAIL>' || $currentUser->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $user = User::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,exhibitor,organizer',
        ]);

        if ($validated['password']) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        return redirect()->route('admin.users.index')
                        ->with('success', 'تم تحديث المستخدم بنجاح');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy($id)
    {
        // Check if user is admin
        $currentUser = Auth::user();
        $isAdmin = $currentUser->email === '<EMAIL>' || $currentUser->role === 'admin';
        
        if (!$isAdmin) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $user = User::findOrFail($id);

        // Prevent admin from deleting themselves
        if ($user->id === $currentUser->id) {
            return redirect()->route('admin.users.index')
                           ->with('error', 'لا يمكنك حذف حسابك الخاص');
        }

        // Check if user has bookings
        if ($user->bookings()->count() > 0) {
            return redirect()->route('admin.users.index')
                           ->with('error', 'لا يمكن حذف المستخدم لأن لديه حجوزات مرتبطة');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
                        ->with('success', 'تم حذف المستخدم بنجاح');
    }
}
