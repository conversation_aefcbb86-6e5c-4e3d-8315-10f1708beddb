<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->longText('signature_data')->nullable()->after('status');
            $table->timestamp('signature_date')->nullable()->after('signature_data');
            $table->string('signature_ip')->nullable()->after('signature_date');
            $table->text('declaration_text')->nullable()->after('signature_ip');
            $table->boolean('declaration_accepted')->default(false)->after('declaration_text');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn([
                'signature_data',
                'signature_date',
                'signature_ip',
                'declaration_text',
                'declaration_accepted'
            ]);
        });
    }
};
