<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use MyFatoorah\Library\API\Payment\MyFatoorahPayment;
use MyFatoorah\Library\API\Payment\MyFatoorahPaymentStatus;
use Exception;

class PaymentController extends Controller
{
    /**
     * @var array MyFatoorah Configuration
     */
    public $mfConfig = [];

    public function __construct()
    {
        // Use OFFICIAL MyFatoorah Configuration
        $this->mfConfig = [
            'apiKey'      => config('myfatoorah.api_key'),
            'isTest'      => config('myfatoorah.test_mode'),
            'countryCode' => config('myfatoorah.country_iso'),
        ];
    }

    /**
     * Initiate payment for a booking using OFFICIAL MyFatoorah Package
     */
    public function initiatePayment(Request $request)
    {
        try {
            $bookingId = $request->input('booking_id') ?? $request->route('booking');
            $booking = Booking::with(['user', 'booth', 'exhibition'])->findOrFail($bookingId);

            // Get current user ID from session
            $currentUserId = session('user_id', 1); // Default to 1 for testing

            // Ensure user can only pay for their own bookings
            if ($booking->user_id !== $currentUserId) {
                abort(403);
            }

            // Check if booking can be paid for
            if (!in_array($booking->status, ['pending', 'confirmed'])) {
                return back()->withErrors(['payment' => 'This booking cannot be paid for.']);
            }

            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => $booking->user_id,
                'amount' => $booking->total_amount,
                'currency' => 'KWD',
                'status' => 'pending',
                'payment_method' => 'myfatoorah',
                'gateway' => 'MyFatoorah',
            ]);

            // Prepare payment data using OFFICIAL MyFatoorah format
            $curlData = [
                'CustomerName'       => $booking->user->name ?? 'Customer',
                'InvoiceValue'       => $booking->total_amount,
                'DisplayCurrencyIso' => 'KWD',
                'CustomerEmail'      => $booking->user->email ?? '<EMAIL>',
                'CallBackUrl'        => url('/payment/callback?booking_id=' . $booking->id),
                'ErrorUrl'           => url('/payment/error?booking_id=' . $booking->id),
                'MobileCountryCode'  => '+965',
                'CustomerMobile'     => $booking->user->phone ?? '12345678',
                'Language'           => 'ar',
                'CustomerReference'  => 'BOOKING-' . $booking->id,
                'SourceInfo'         => 'Laravel ' . app()::VERSION . ' - Season Expo'
            ];

            // Use OFFICIAL MyFatoorah Payment Library
            $paymentMethodId = 0; // 0 = MyFatoorah invoice page (default)
            $sessionId = null;
            $orderId = $booking->id;

            Log::info('MyFatoorah Payment Request (OFFICIAL)', [
                'booking_id' => $booking->id,
                'config' => $this->mfConfig,
                'data' => $curlData
            ]);

            // Create MyFatoorah Payment Object
            $mfObj = new MyFatoorahPayment($this->mfConfig);
            $payment_result = $mfObj->getInvoiceURL($curlData, $paymentMethodId, $orderId, $sessionId);

            // Update payment record
            $payment->update([
                'gateway_transaction_id' => $payment_result['invoiceId'] ?? 'INVOICE-' . time(),
                'gateway_response' => json_encode($payment_result),
            ]);

            Log::info('MyFatoorah Payment Success (OFFICIAL)', [
                'booking_id' => $booking->id,
                'invoice_url' => $payment_result['invoiceURL'],
                'invoice_id' => $payment_result['invoiceId']
            ]);

            // Redirect to MyFatoorah
            return redirect()->away($payment_result['invoiceURL']);

        } catch (Exception $e) {
            Log::error('MyFatoorah Payment Failed (OFFICIAL)', [
                'booking_id' => $booking->id ?? 'Unknown',
                'error' => $e->getMessage(),
                'config' => $this->mfConfig
            ]);

            return back()->withErrors(['payment' => 'فشل في بدء عملية الدفع: ' . $e->getMessage()]);
        }
    }



    /**
     * Handle payment callback using OFFICIAL MyFatoorah Package
     */
    public function paymentCallback(Request $request): RedirectResponse
    {
        try {
            $paymentId = $request->get('paymentId');

            if (!$paymentId) {
                return redirect()->route('dashboard')
                    ->withErrors(['payment' => 'Invalid payment response.']);
            }

            // Use OFFICIAL MyFatoorah Payment Status
            $mfObj = new MyFatoorahPaymentStatus($this->mfConfig);
            $data = $mfObj->getPaymentStatus($paymentId, 'PaymentId');

            Log::info('MyFatoorah Callback (OFFICIAL)', [
                'payment_id' => $paymentId,
                'status' => $data->InvoiceStatus,
                'reference' => $data->CustomerReference
            ]);

            // Find booking by CustomerReference
            $bookingId = $data->CustomerReference;
            if (strpos($bookingId, 'BOOKING-') === 0) {
                $bookingId = explode('-', $bookingId)[1];
            }

            $booking = Booking::findOrFail($bookingId);
            $payment = $booking->payments()->where('status', 'pending')->first();

            if ($data->InvoiceStatus === 'Paid') {
                // Payment successful
                $payment->update([
                    'status' => 'completed',
                    'gateway_response' => json_encode($data),
                    'payment_date' => now(),
                    'transaction_id' => $data->InvoiceTransactions[0]->TransactionId ?? null,
                ]);

                // Update booking status
                $booking->update(['status' => 'confirmed']);

                // Update booth status
                $booking->booth->update(['status' => 'booked']);

                return redirect()->route('bookings.show', $booking)
                    ->with('success', 'تم الدفع بنجاح! تم تأكيد حجز الجناح.');

            } else {
                // Payment failed
                $payment->update([
                    'status' => 'failed',
                    'gateway_response' => json_encode($data),
                    'failed_at' => now(),
                    'failure_reason' => $data->InvoiceStatus,
                ]);

                return redirect()->route('bookings.show', $booking)
                    ->withErrors(['payment' => 'فشل في الدفع: ' . $data->InvoiceStatus]);
            }

        } catch (Exception $e) {
            Log::error('MyFatoorah Callback Error (OFFICIAL)', [
                'payment_id' => $paymentId ?? 'Unknown',
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dashboard')
                ->withErrors(['payment' => 'خطأ في معالجة الدفع: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle payment error from MyFatoorah
     */
    public function paymentError(Request $request): RedirectResponse
    {
        return redirect()->route('dashboard')
            ->withErrors(['payment' => 'Payment was cancelled or failed.']);
    }

    /**
     * Handle webhook from MyFatoorah
     */
    public function webhook(Request $request)
    {
        try {
            // Verify webhook signature
            $webhookSecretKey = config('myfatoorah.webhook_secret_key');
            $signature = $request->header('MyFatoorah-Signature');

            if ($signature !== hash_hmac('sha256', $request->getContent(), $webhookSecretKey)) {
                return response('Unauthorized', 401);
            }

            $data = $request->json()->all();
            $eventType = $data['EventType'] ?? '';

            if ($eventType === 'TransactionStatusChanged') {
                $invoiceId = $data['Data']['InvoiceId'];

                // Get payment details (simplified for now)
                $paymentData = (object) ['InvoiceStatus' => 'Paid'];

                // Find and update payment record
                $payment = Payment::where('gateway_transaction_id', $invoiceId)->first();

                if ($payment) {
                    $booking = $payment->booking;

                    if ($paymentData->InvoiceStatus === 'Paid') {
                        $payment->update([
                            'status' => 'completed',
                            'payment_date' => now(),
                        ]);

                        $booking->update(['status' => 'confirmed']);
                        $booking->booth->update(['status' => 'booked']);
                    }
                }
            }

            return response('OK', 200);

        } catch (Exception $e) {
            return response('Error: ' . $e->getMessage(), 500);
        }
    }
}
