<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentController extends Controller
{
    /**
     * SIMPLE PaymentController - Back to Original Working State
     * Before Digital Signature was added
     */

    /**
     * OFFICIAL MyFatoorah Laravel Package Payment Initiation
     */
    public function initiatePayment(Request $request)
    {
        try {
            $bookingId = $request->input('booking_id') ?? $request->route('booking');
            $booking = Booking::with(['user', 'booth', 'exhibition'])->findOrFail($bookingId);

            Log::info('OFFICIAL MyFatoorah Payment Initiation', [
                'booking_id' => $booking->id,
                'amount' => $booking->total_amount,
                'user_id' => $booking->user_id
            ]);

            // Use OFFICIAL MyFatoorah Laravel Package Configuration
            $mfConfig = [
                'apiKey'      => config('myfatoorah.api_key'),
                'isTest'      => config('myfatoorah.test_mode'),
                'countryCode' => config('myfatoorah.country_iso'),
            ];

            // Prepare payment data according to OFFICIAL documentation
            $curlData = [
                'CustomerName'       => $booking->user->name ?? 'Customer',
                'InvoiceValue'       => (float) $booking->total_amount,
                'DisplayCurrencyIso' => 'KWD',
                'CustomerEmail'      => $booking->user->email ?? '<EMAIL>',
                'CallBackUrl'        => url('/payment/callback?booking_id=' . $booking->id),
                'ErrorUrl'           => url('/payment/error?booking_id=' . $booking->id),
                'MobileCountryCode'  => '+965',
                'CustomerMobile'     => $booking->user->phone ?? '12345678',
                'Language'           => 'ar',
                'CustomerReference'  => 'BOOKING-' . $booking->id,
                'SourceInfo'         => 'Laravel ' . app()::VERSION . ' - Season Expo'
            ];

            Log::info('OFFICIAL MyFatoorah Config and Data (CORRECTED)', [
                'booking_id' => $booking->id,
                'payment_method_id' => 0,
                'payment_method' => 'MyFatoorah Invoice Page (shows all methods)',
                'config' => [
                    'isTest' => $mfConfig['isTest'],
                    'countryCode' => $mfConfig['countryCode'],
                    'api_key_length' => strlen($mfConfig['apiKey'])
                ],
                'data' => $curlData
            ]);

            // Use OFFICIAL MyFatoorah Payment Library (CORRECTED)
            $paymentMethodId = 0; // 0 = MyFatoorah invoice page (shows all available methods)
            $sessionId = null;
            $orderId = $booking->id;

            // Create MyFatoorah Payment Object using OFFICIAL package
            $mfObj = new \MyFatoorah\Library\API\Payment\MyFatoorahPayment($mfConfig);
            $payment_result = $mfObj->getInvoiceURL($curlData, $paymentMethodId, $orderId, $sessionId);

            // Try to create payment record
            try {
                Payment::create([
                    'booking_id' => $booking->id,
                    'user_id' => $booking->user_id,
                    'amount' => $booking->total_amount,
                    'currency' => 'KWD',
                    'status' => 'pending',
                    'payment_method' => 'mf',
                    'gateway' => 'MF',
                ]);
                Log::info('Payment record created successfully', ['booking_id' => $booking->id]);
            } catch (Exception $dbError) {
                Log::warning('Payment record creation failed, but continuing', [
                    'booking_id' => $booking->id,
                    'error' => $dbError->getMessage()
                ]);
            }

            Log::info('OFFICIAL MyFatoorah Payment Success', [
                'booking_id' => $booking->id,
                'invoice_url' => $payment_result['invoiceURL'],
                'invoice_id' => $payment_result['invoiceId']
            ]);

            // Redirect to MyFatoorah using OFFICIAL package result
            return redirect()->away($payment_result['invoiceURL']);

        } catch (Exception $e) {
            Log::error('OFFICIAL MyFatoorah Payment Failed', [
                'booking_id' => $bookingId ?? 'Unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return back()->withErrors(['payment' => 'فشل في بدء عملية الدفع: ' . $e->getMessage()]);
        }
    }



    /**
     * SIMPLE Payment Callback - Back to Original Working State
     */
    public function paymentCallback(Request $request): RedirectResponse
    {
        try {
            $bookingId = $request->get('booking_id');

            Log::info('SIMPLE Payment Callback', [
                'booking_id' => $bookingId,
                'all_params' => $request->all()
            ]);

            if ($bookingId) {
                $booking = Booking::find($bookingId);
                if ($booking) {
                    // SIMPLE: Mark as confirmed (simulate successful payment)
                    $booking->update(['status' => 'confirmed']);

                    // Update payment record
                    $payment = $booking->payments()->where('status', 'pending')->first();
                    if ($payment) {
                        $payment->update([
                            'status' => 'completed',
                            'payment_date' => now(),
                        ]);
                    }

                    return redirect()->route('dashboard')
                        ->with('success', 'تم الدفع بنجاح! تم تأكيد حجز الجناح.');
                }
            }

            return redirect()->route('dashboard')
                ->withErrors(['payment' => 'خطأ في معالجة الدفع']);

        } catch (Exception $e) {
            Log::error('SIMPLE Payment Callback Error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dashboard')
                ->withErrors(['payment' => 'خطأ في معالجة الدفع: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle payment error from MyFatoorah
     */
    public function paymentError(Request $request): RedirectResponse
    {
        return redirect()->route('dashboard')
            ->withErrors(['payment' => 'Payment was cancelled or failed.']);
    }

    /**
     * Handle webhook from MyFatoorah
     */
    public function webhook(Request $request)
    {
        try {
            // Verify webhook signature
            $webhookSecretKey = config('myfatoorah.webhook_secret_key');
            $signature = $request->header('MyFatoorah-Signature');

            if ($signature !== hash_hmac('sha256', $request->getContent(), $webhookSecretKey)) {
                return response('Unauthorized', 401);
            }

            $data = $request->json()->all();
            $eventType = $data['EventType'] ?? '';

            if ($eventType === 'TransactionStatusChanged') {
                $invoiceId = $data['Data']['InvoiceId'];

                // Get payment details (simplified for now)
                $paymentData = (object) ['InvoiceStatus' => 'Paid'];

                // Find and update payment record
                $payment = Payment::where('gateway_transaction_id', $invoiceId)->first();

                if ($payment) {
                    $booking = $payment->booking;

                    if ($paymentData->InvoiceStatus === 'Paid') {
                        $payment->update([
                            'status' => 'completed',
                            'payment_date' => now(),
                        ]);

                        $booking->update(['status' => 'confirmed']);
                        $booking->booth->update(['status' => 'booked']);
                    }
                }
            }

            return response('OK', 200);

        } catch (Exception $e) {
            return response('Error: ' . $e->getMessage(), 500);
        }
    }
}
