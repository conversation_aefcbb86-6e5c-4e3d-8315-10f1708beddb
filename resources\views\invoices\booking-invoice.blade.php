<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>فاتورة الحجز #{{ $booking->id }} - Season Expo Kuwait</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .invoice-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .invoice-header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .invoice-body {
            padding: 30px;
        }
        
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .invoice-info div {
            flex: 1;
            min-width: 250px;
            margin-bottom: 20px;
        }
        
        .invoice-info h3 {
            color: #1e40af;
            margin-bottom: 10px;
            font-size: 1.2rem;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .invoice-info p {
            margin: 5px 0;
            color: #666;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .invoice-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: bold;
            padding: 15px;
            text-align: right;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .invoice-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .invoice-table tr:hover {
            background: #f9fafb;
        }
        
        .total-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 1.1rem;
        }
        
        .total-row.final {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1e40af;
            border-top: 2px solid #3b82f6;
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-confirmed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-cancelled {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .footer {
            background: #f8fafc;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }
        
        .print-date {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-top: 20px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                border: none;
                border-radius: 0;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <h1>Season Expo Kuwait</h1>
            <p>فاتورة حجز الجناح</p>
        </div>

        <!-- Body -->
        <div class="invoice-body">
            <!-- Invoice Info -->
            <div class="invoice-info">
                <div>
                    <h3>📋 معلومات الفاتورة</h3>
                    <p><strong>رقم الفاتورة:</strong> INV-{{ $booking->id }}</p>
                    <p><strong>رقم الحجز:</strong> #{{ $booking->id }}</p>
                    <p><strong>تاريخ الإصدار:</strong> {{ now()->format('d/m/Y') }}</p>
                    <p><strong>تاريخ الحجز:</strong> {{ $booking->created_at->format('d/m/Y') }}</p>
                    <p><strong>الحالة:</strong> 
                        @if($booking->status == 'confirmed')
                            <span class="status-badge status-confirmed">مؤكد</span>
                        @elseif($booking->status == 'pending')
                            <span class="status-badge status-pending">قيد الانتظار</span>
                        @else
                            <span class="status-badge status-cancelled">ملغي</span>
                        @endif
                    </p>
                </div>

                <div>
                    <h3>👤 معلومات العميل</h3>
                    <p><strong>الاسم:</strong> {{ $booking->user->name }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ $booking->user->email }}</p>
                    @if($booking->user->phone)
                        <p><strong>الهاتف:</strong> {{ $booking->user->phone }}</p>
                    @endif
                    @if($booking->user->company)
                        <p><strong>الشركة:</strong> {{ $booking->user->company }}</p>
                    @endif
                </div>
            </div>

            <!-- Booking Details Table -->
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>التفاصيل</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>المعرض</strong></td>
                        <td>{{ $booking->exhibition->title ?? 'غير محدد' }}</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td><strong>رقم الجناح</strong></td>
                        <td>{{ $booking->booth->booth_number ?? 'غير محدد' }}</td>
                        <td>-</td>
                    </tr>
                    @if($booking->booth && $booking->booth->area)
                        <tr>
                            <td><strong>مساحة الجناح</strong></td>
                            <td>{{ $booking->booth->area }} متر مربع</td>
                            <td>-</td>
                        </tr>
                    @endif
                    <tr>
                        <td><strong>رسوم الحجز</strong></td>
                        <td>حجز جناح في {{ $booking->exhibition->title ?? 'المعرض' }}</td>
                        <td>{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</td>
                    </tr>
                </tbody>
            </table>

            <!-- Total Section -->
            <div class="total-section">
                <div class="total-row">
                    <span>المبلغ الفرعي:</span>
                    <span>{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</span>
                </div>
                <div class="total-row">
                    <span>الضرائب:</span>
                    <span>0.00 {{ $booking->currency ?? 'د.ك' }}</span>
                </div>
                <div class="total-row final">
                    <span>المبلغ الإجمالي:</span>
                    <span>{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</span>
                </div>
            </div>

            <!-- Payment Info -->
            @if($booking->payments && $booking->payments->count() > 0)
                <div style="margin-top: 30px;">
                    <h3 style="color: #1e40af; margin-bottom: 15px;">💳 معلومات الدفع</h3>
                    @foreach($booking->payments as $payment)
                        <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <p><strong>طريقة الدفع:</strong> {{ $payment->payment_method ?? 'غير محدد' }}</p>
                            <p><strong>حالة الدفع:</strong> 
                                @if($payment->status == 'completed')
                                    <span class="status-badge status-confirmed">مكتمل</span>
                                @elseif($payment->status == 'pending')
                                    <span class="status-badge status-pending">قيد المعالجة</span>
                                @else
                                    <span class="status-badge status-cancelled">فاشل</span>
                                @endif
                            </p>
                            @if($payment->paid_at)
                                <p><strong>تاريخ الدفع:</strong> {{ $payment->paid_at->format('d/m/Y H:i') }}</p>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Season Expo Kuwait</strong></p>
            <p>شكراً لاختياركم معارضنا. نتطلع لرؤيتكم في المعرض!</p>
            <p>للاستفسارات: <EMAIL> | +965 1234 5678</p>
        </div>
    </div>

    <div class="print-date">
        تم إنشاء هذه الفاتورة في: {{ now()->format('d/m/Y H:i:s') }}
    </div>

    <script>
        // Auto-print when opened as download
        window.onload = function() {
            if (window.location.search.includes('print=1')) {
                window.print();
            }
        };
    </script>
</body>
</html>
