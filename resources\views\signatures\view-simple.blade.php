<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>عرض الإقرار والتعهد - الحجز #{{ $booking->id }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }

            body {
                font-family: 'Times New Roman', serif !important;
                font-size: 14pt !important;
                line-height: 2 !important;
                margin: 0;
                padding: 15mm;
                direction: rtl;
            }

            .bg-white { background: white !important; }
            .shadow-lg { box-shadow: none !important; }
            .rounded-lg { border-radius: 0 !important; }
            .text-blue-600, .text-green-600 { color: black !important; }
            .bg-gray-50 { background: white !important; }
            .border { border: 2px solid black !important; }
            .border-2 { border: 2px solid black !important; }
            .border-black { border-color: black !important; }
            .border-b { border-bottom: 1px solid black !important; }
            h1, h2, h3 { color: black !important; }
            .p-6 { padding: 1.5rem !important; }
            .p-8 { padding: 20mm !important; }
            .mb-8 { margin-bottom: 2rem !important; }
            .mt-12 { margin-top: 3rem !important; }
            .text-2xl { font-size: 18pt !important; font-weight: bold !important; }
            .text-xl { font-size: 16pt !important; font-weight: bold !important; }
            .leading-loose { line-height: 2.2 !important; }
            .leading-relaxed { line-height: 2 !important; }
            .text-justify { text-align: justify !important; }
            .text-right { text-align: right !important; }
            .text-center { text-align: center !important; }
            .w-64 { width: 200px !important; }
            .flex { display: flex !important; }
            .justify-between { justify-content: space-between !important; }
            .items-center { align-items: center !important; }
            .font-bold { font-weight: bold !important; }
            .mb-4 { margin-bottom: 1rem !important; }
            .mb-6 { margin-bottom: 1.5rem !important; }
            .space-y-4 > * + * { margin-top: 1rem !important; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h1 class="text-2xl font-bold text-gray-900">📄 عرض الإقرار والتعهد</h1>
                    <div class="flex gap-3 no-print">
                        <button onclick="window.print()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            🖨️ طباعة
                        </button>
                        <a href="/my-bookings" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            ← العودة للحجوزات
                        </a>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div>
                        <h3 class="font-bold text-gray-700 mb-2">معلومات الحجز:</h3>
                        <p><strong>رقم الحجز:</strong> #{{ $booking->id }}</p>
                        <p><strong>المعرض:</strong> {{ $booking->exhibition->title ?? 'غير محدد' }}</p>
                        <p><strong>رقم الجناح:</strong> {{ $booking->booth->booth_number ?? 'غير محدد' }}</p>
                        <p><strong>تاريخ الحجز:</strong> {{ $booking->created_at->format('d/m/Y') }}</p>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-700 mb-2">معلومات العميل:</h3>
                        <p><strong>الاسم:</strong> {{ $booking->user->name }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ $booking->user->email }}</p>
                        @if($booking->user->company)
                            <p><strong>الشركة:</strong> {{ $booking->user->company }}</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Digital Signature Content -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-blue-900 mb-2">إقرار وتعهد</h2>
                    <p class="text-lg text-gray-600">الشركات المشاركة في المعارض التجارية المؤقتة</p>
                    <p class="text-sm text-gray-500 mt-2">وفقاً للقرار الوزاري رقم 303/2018</p>
                </div>

                <div class="space-y-6 text-gray-800 leading-relaxed">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="font-bold text-blue-900 mb-4">📋 بيانات الشركة المشاركة:</h3>
                        
                        @if($signature && isset($signature->metadata))
                            @php
                                $metadata = is_string($signature->metadata) ? json_decode($signature->metadata, true) : $signature->metadata;
                            @endphp
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p><strong>الاسم التجاري:</strong> {{ $metadata['trade_name'] ?? 'غير محدد' }}</p>
                                    <p><strong>اسم صاحب الرخصة:</strong> {{ $metadata['license_holder_name'] ?? 'غير محدد' }}</p>
                                </div>
                                <div>
                                    <p><strong>رقم الرخصة التجارية:</strong> {{ $metadata['commercial_license_number'] ?? 'غير محدد' }}</p>
                                    <p><strong>تاريخ الرخصة:</strong> {{ isset($metadata['license_date']) ? \Carbon\Carbon::parse($metadata['license_date'])->format('d/m/Y') : 'غير محدد' }}</p>
                                </div>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-gray-500">لم يتم العثور على بيانات التوقيع الإلكتروني</p>
                                <p class="text-sm text-gray-400 mt-2">قد يكون الإقرار لم يتم توقيعه بعد أو تم حذف البيانات</p>
                            </div>
                        @endif
                    </div>

                    <div class="space-y-4">
                        <h3 class="font-bold text-gray-900 text-xl">📝 نص الإقرار والتعهد:</h3>

                        <div class="bg-white border-2 border-black p-8" style="font-family: 'Times New Roman', serif;">
                            <div class="text-center mb-8">
                                <h2 class="text-2xl font-bold mb-4">إقرار وتعهد</h2>
                                <h3 class="text-xl font-bold">الشركات المشاركة</h3>
                            </div>

                            <div class="text-right leading-relaxed space-y-4" style="font-size: 16px; line-height: 2;">
                                <p class="mb-6">
                                    أتعهد أنا الموقع أدناه
                                    @if($signature && isset($signature->metadata))
                                        @php
                                            $metadata = is_string($signature->metadata) ? json_decode($signature->metadata, true) : $signature->metadata;
                                        @endphp
                                        <strong>{{ $metadata['license_holder_name'] ?? '................................................................' }}</strong>
                                    @else
                                        ................................................................
                                    @endif
                                    <br>
                                    المشارك في معرض <strong>{{ $booking->exhibition->title ?? '................................................................' }}</strong><br>
                                    المزمع إقامته خلال الفترة
                                    @if($booking->exhibition && $booking->exhibition->start_date)
                                        <strong>{{ \Carbon\Carbon::parse($booking->exhibition->start_date)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($booking->exhibition->end_date)->format('d/m/Y') }}</strong>
                                    @else
                                        ................................................................
                                    @endif
                                </p>

                                <p class="text-justify leading-loose">
                                    بأن ألتزم بكل ما تم ذكره في القرار الوزاري رقم (303) لسنة 2018 بشأن القواعد العامة
                                    لتنظيم إقامة المعارض التجارية المؤقتة بدولة الكويت، وأقر بصحة كل ما ورد في كشف البيانات
                                    والخدمات الخاص بي والمقدم من المنظم، وأتحمل كافة الإجراءات القانونية التي قد تتخذها
                                    الوزارة بحالة عدم التزامي بهذا التعهد ورد جميع المستندات إلى أصحابها وتعويضهم
                                    التعويض الكامل أتعهد في حالة ثبوت عدم صحة الأضرار التي لحقت بهم في حالة ثبوت عدم صحة البيانات
                                    والمستندات أو أتضاح أو هميتها.
                                </p>

                                <p class="text-justify leading-loose">
                                    كما أتعهد بإعطاء فترة في حال الشراء بالمعرض وكذلك الالتزام بمراعاة الآداب العامة
                                    وعدم عرض مايخدش بالحياء، وكذلك عدم إقامة عرض أزياء، والتزم بعدم دخولي أي دولة
                                    مالم تكن هناك موافقة مسبقة من الوزارة، وأتعهد بتسهيل مأمورية المكلفين من الجهات
                                    المختصة بالرقابة والإشراف على المعرض.
                                </p>

                                <div class="mt-12 space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span>الاسم التجاري:</span>
                                        <div class="border-b border-black w-64 text-center">
                                            @if($signature && isset($signature->metadata))
                                                {{ $metadata['trade_name'] ?? '' }}
                                            @endif
                                        </div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>اسم صاحب الترخيص:</span>
                                        <div class="border-b border-black w-64 text-center">
                                            @if($signature && isset($signature->metadata))
                                                {{ $metadata['license_holder_name'] ?? '' }}
                                            @endif
                                        </div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>رقم الترخيص التجاري:</span>
                                        <div class="border-b border-black w-64 text-center">
                                            @if($signature && isset($signature->metadata))
                                                {{ $metadata['commercial_license_number'] ?? '' }}
                                            @endif
                                        </div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>تاريخ:</span>
                                        <div class="border-b border-black w-64 text-center">
                                            @if($signature && isset($signature->metadata))
                                                {{ isset($metadata['license_date']) ? \Carbon\Carbon::parse($metadata['license_date'])->format('d/m/Y') : '' }}
                                            @endif
                                        </div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <span>التوقيع:</span>
                                        <div class="border-b border-black w-64 text-center">
                                            @if($signature)
                                                <span class="text-green-600 font-bold">✅ تم التوقيع إلكترونياً</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Signature Info -->
                    @if($signature)
                        <div class="bg-green-50 p-6 rounded-lg border border-green-200">
                            <h3 class="font-bold text-green-900 mb-4">✅ معلومات التوقيع الإلكتروني:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p><strong>اسم الموقع:</strong> {{ $signature->signer_name ?? 'غير محدد' }}</p>
                                    <p><strong>البريد الإلكتروني:</strong> {{ $signature->signer_email ?? 'غير محدد' }}</p>
                                </div>
                                <div>
                                    <p><strong>تاريخ التوقيع:</strong> {{ $signature->created_at ? $signature->created_at->format('d/m/Y H:i:s') : 'غير محدد' }}</p>
                                    <p><strong>عنوان IP:</strong> {{ $signature->signer_ip ?? 'غير محدد' }}</p>
                                </div>
                            </div>
                            
                            <div class="mt-4 p-4 bg-white rounded border">
                                <p class="text-center font-bold text-green-800">
                                    ✅ تم التوقيع الإلكتروني بنجاح
                                </p>
                                <p class="text-center text-sm text-gray-600 mt-2">
                                    هذا الإقرار موقع إلكترونياً ومعتمد قانونياً
                                </p>
                            </div>
                        </div>
                    @else
                        <div class="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
                            <h3 class="font-bold text-yellow-900 mb-2">⚠️ تنبيه:</h3>
                            <p class="text-yellow-800">لم يتم العثور على توقيع إلكتروني لهذا الإقرار.</p>
                            <p class="text-sm text-yellow-700 mt-2">قد يكون الإقرار لم يتم توقيعه بعد أو تم حذف بيانات التوقيع.</p>
                        </div>
                    @endif
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-200 text-center text-sm text-gray-500">
                    <p><strong>Season Expo Kuwait</strong></p>
                    <p>تم إنشاء هذا المستند في: {{ now()->format('d/m/Y H:i:s') }}</p>
                    <p>للاستفسارات: <EMAIL></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
