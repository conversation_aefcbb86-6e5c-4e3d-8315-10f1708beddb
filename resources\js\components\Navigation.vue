<template>
  <nav :class="navClass" class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Logo Section -->
        <div class="flex items-center">
          <Logo 
            :variant="logoVariant"
            :size="logoSize"
            :href="homeUrl"
          />
        </div>

        <!-- Navigation Links -->
        <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
          <!-- Language Switcher (if available) -->
          <LanguageSwitcher 
            v-if="showLanguageSwitcher && currentLocale" 
            :current-locale="currentLocale" 
          />
          
          <!-- Custom Navigation Links -->
          <slot name="nav-links">
            <!-- Default navigation links -->
            <Link 
              v-if="showExhibitions"
              :href="route('exhibitions.index')" 
              class="nav-link text-blue-600 font-semibold"
            >
              {{ t('exhibitions') }}
            </Link>
            
            <!-- Authenticated User Links -->
            <template v-if="user">
              <Link
                :href="route('dashboard')"
                class="nav-link text-gray-600"
              >
                {{ t('dashboard') }}
              </Link>
              
              <!-- User Menu Dropdown -->
              <div class="relative" v-if="showUserMenu">
                <button
                  @click="toggleUserMenu"
                  class="flex items-center text-gray-600 hover:text-gray-900 focus:outline-none"
                >
                  <span class="ml-2">{{ user.name }}</span>
                  <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>
                
                <!-- Dropdown Menu -->
                <div
                  v-show="userMenuOpen"
                  class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"
                  @click.away="userMenuOpen = false"
                >
                  <Link
                    :href="route('profile.edit')"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {{ t('profile') }}
                  </Link>
                  <Link
                    :href="route('logout')"
                    method="post"
                    as="button"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {{ t('logout') }}
                  </Link>
                </div>
              </div>
            </template>
            
            <!-- Guest Links -->
            <template v-else>
              <Link
                :href="route('login')"
                class="nav-link text-gray-600"
              >
                {{ t('sign_in') }}
              </Link>
              <Link
                :href="route('register')"
                class="nav-link bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {{ t('sign_up') }}
              </Link>
            </template>
          </slot>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import Logo from './Logo.vue';

// Import LanguageSwitcher if available
let LanguageSwitcher = null;
try {
  LanguageSwitcher = require('./LanguageSwitcher.vue').default;
} catch (e) {
  // LanguageSwitcher not available
}

const props = defineProps({
  // Navigation styling
  navClass: {
    type: String,
    default: ''
  },
  // Logo variant
  logoVariant: {
    type: String,
    default: 'default'
  },
  // Logo size
  logoSize: {
    type: String,
    default: 'md'
  },
  // Home URL
  homeUrl: {
    type: String,
    default: '/'
  },
  // Current user
  user: {
    type: Object,
    default: null
  },
  // Current locale
  currentLocale: {
    type: String,
    default: null
  },
  // Show language switcher
  showLanguageSwitcher: {
    type: Boolean,
    default: false
  },
  // Show exhibitions link
  showExhibitions: {
    type: Boolean,
    default: true
  },
  // Show user menu dropdown
  showUserMenu: {
    type: Boolean,
    default: true
  },
  // RTL support
  isRTL: {
    type: Boolean,
    default: false
  }
});

// User menu state
const userMenuOpen = ref(false);

const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value;
};

// Translation function (fallback if not available)
const t = (key) => {
  // Try to use global translation function if available
  if (window.t && typeof window.t === 'function') {
    return window.t(key);
  }
  
  // Fallback translations
  const translations = {
    'exhibitions': 'المعارض',
    'dashboard': 'لوحة التحكم',
    'sign_in': 'تسجيل الدخول',
    'sign_up': 'إنشاء حساب',
    'profile': 'الملف الشخصي',
    'logout': 'تسجيل الخروج'
  };
  
  return translations[key] || key;
};

// Route function (fallback if not available)
const route = (name, params = {}) => {
  // Try to use global route function if available
  if (window.route && typeof window.route === 'function') {
    return window.route(name, params);
  }
  
  // Fallback routes
  const routes = {
    'exhibitions.index': '/exhibitions',
    'dashboard': '/dashboard',
    'login': '/login',
    'register': '/register',
    'profile.edit': '/profile',
    'logout': '/logout'
  };
  
  return routes[name] || '/';
};
</script>

<style scoped>
.nav-link {
  @apply transition-colors duration-200;
}

.nav-link:hover {
  @apply text-blue-600;
}

/* Print styles */
@media print {
  nav {
    display: none !important;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .nav-link {
    @apply text-sm;
  }
}
</style>
