<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';

const props = defineProps({
  exhibitions: Object,
  categories: Array,
  filters: Object,
  locale: String,
  translations: Object,
});

console.log('Exhibitions Props:', props);
console.log('First Exhibition:', props.exhibitions?.data?.[0]);
console.log('First Exhibition Image:', props.exhibitions?.data?.[0]?.featured_image);

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Translation helper
const t = (key) => {
  return props.translations?.app?.[key] || key;
};

// Image URL helper
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http')) {
    return imagePath;
  }

  // If it starts with storage/, add the asset URL
  if (imagePath.startsWith('storage/')) {
    return `/storage/${imagePath.replace('storage/', '')}`;
  }

  // If it's just a filename, assume it's in storage/exhibitions
  if (!imagePath.includes('/')) {
    return `/storage/exhibitions/${imagePath}`;
  }

  // Default: prepend /storage/
  return `/storage/${imagePath}`;
};

// Handle image loading errors
const handleImageError = (event) => {
  // Replace broken image with placeholder
  const img = event.target;
  const placeholder = img.parentElement.querySelector('.image-placeholder');

  if (placeholder) {
    img.style.display = 'none';
    placeholder.style.display = 'flex';
  } else {
    // Create placeholder if it doesn't exist
    const placeholderDiv = document.createElement('div');
    placeholderDiv.className = 'w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100 image-placeholder';
    placeholderDiv.innerHTML = `
      <div class="text-center">
        <div class="text-4xl mb-2">🎪</div>
        <div class="text-sm text-gray-600">Exhibition Image</div>
      </div>
    `;
    img.style.display = 'none';
    img.parentElement.appendChild(placeholderDiv);
  }
};
</script>

<template>
  <Head title="المعارض" />

  <div class="min-h-screen bg-gray-50">
    <!-- Simple Navigation -->
    <nav class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="hover:opacity-80 transition-opacity">
              <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
            </Link>
          </div>
          <div class="flex items-center space-x-4">
            <Link href="/exhibitions" class="text-blue-600 font-semibold">Exhibitions</Link>
            <Link href="/dashboard" class="text-gray-600">Dashboard</Link>
          </div>
        </div>
      </div>
    </nav>

    <div class="py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">All Exhibitions</h1>
          <p class="text-gray-600">
            Discover amazing exhibitions and find the perfect booth for your business
          </p>
        </div>



      <!-- Simple Stats -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="text-sm text-gray-600">
          Showing {{ exhibitions?.meta?.from || 0 }} to {{ exhibitions?.meta?.to || 0 }}
          of {{ exhibitions?.meta?.total || 0 }} exhibitions
        </div>
        <!-- Debug Info -->
        <div class="mt-2 text-xs text-red-600" v-if="exhibitions?.data?.[0]">
          Debug: First exhibition image = {{ exhibitions.data[0].featured_image || 'NO IMAGE' }}
        </div>
      </div>

      <!-- Exhibitions Grid -->
      <div v-if="exhibitions?.data?.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
        <div
          v-for="exhibition in exhibitions.data"
          :key="exhibition.id"
          class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
        >
          <div class="h-48 bg-gray-200 relative">
            <img
              v-if="exhibition.featured_image"
              :src="getImageUrl(exhibition.featured_image)"
              :alt="exhibition.title"
              class="w-full h-full object-cover"
              @error="handleImageError"
            />
            <!-- Placeholder when no image -->
            <div
              v-else
              class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100"
            >
              <div class="text-center">
                <div class="text-4xl mb-2">🎪</div>
                <div class="text-sm text-gray-600">Exhibition Image</div>
              </div>
            </div>
            <div class="absolute top-4 left-4">
              <span
                class="px-3 py-1 rounded-full text-sm font-medium text-white"
                :style="{ backgroundColor: exhibition.category?.color || '#3B82F6' }"
              >
                {{ exhibition.category?.name || 'General' }}
              </span>
            </div>
          </div>

          <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              {{ exhibition.title }}
            </h3>
            <p class="text-gray-600 mb-4 line-clamp-2">
              {{ exhibition.short_description }}
            </p>

            <div class="flex items-center text-sm text-gray-500 mb-4">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
              </svg>
              {{ exhibition.city }}, {{ exhibition.country }}
            </div>

            <div class="flex items-center text-sm text-gray-500 mb-4">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
              </svg>
              {{ formatDate(exhibition.start_date) }} - {{ formatDate(exhibition.end_date) }}
            </div>

            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-500">
                by {{ exhibition.organizer?.name || 'Unknown' }}
              </div>
              <Link
                :href="`/exhibitions/${exhibition.slug}`"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                View Details
              </Link>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
          <svg class="w-24 h-24 mx-auto" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V4z" clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No exhibitions found</h3>
        <p class="text-gray-600 mb-4">
          Try adjusting your search criteria or browse all exhibitions
        </p>
      </div>

      <!-- Pagination -->
      <div v-if="exhibitions?.links && exhibitions.links.length > 3" class="flex justify-center">
        <nav class="flex space-x-2">
          <Link
            v-for="link in exhibitions.links"
            :key="link.label"
            :href="link.url"
            :class="[
              'px-3 py-2 rounded-md text-sm font-medium',
              link.active
                ? 'bg-blue-600 text-white'
                : link.url
                  ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            ]"
          >
            {{ link.label }}
          </Link>
        </nav>
      </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
