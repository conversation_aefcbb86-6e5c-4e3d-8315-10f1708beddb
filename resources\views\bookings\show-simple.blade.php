<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تفاصيل الحجز - Season Expo</title>

    <!-- Custom Favicon -->
    <link rel="icon" type="image/svg+xml" href="/season-expo-favicon.svg">
    <link rel="icon" type="image/x-icon" href="/season-expo-favicon.ico">
    <link rel="shortcut icon" href="/season-expo-favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-2xl ml-3">✅</span>
                        <span>{{ session('success') }}</span>
                    </div>
                </div>
            @endif

            @if($errors->any())
                <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-2xl ml-3">❌</span>
                        <div>
                            @foreach($errors->all() as $error)
                                <p>{{ $error }}</p>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Booking Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">تفاصيل الحجز</h1>
                        <p class="text-gray-600">رقم الحجز: #{{ $booking->id }}</p>
                    </div>
                    <span class="px-3 py-1 text-sm font-medium rounded-full
                        {{ $booking->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                        {{ $booking->status === 'confirmed' ? 'bg-green-100 text-green-800' : '' }}
                        {{ $booking->status === 'cancelled' ? 'bg-red-100 text-red-800' : '' }}">
                        {{ $booking->status === 'pending' ? 'في انتظار الدفع' : '' }}
                        {{ $booking->status === 'confirmed' ? 'مؤكد' : '' }}
                        {{ $booking->status === 'cancelled' ? 'ملغي' : '' }}
                    </span>
                </div>

                <!-- Booking Details Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Booth Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">🏢 معلومات الجناح</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">رقم الجناح:</span>
                                <span class="font-medium">{{ $booking->booth->booth_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الحجم:</span>
                                <span class="font-medium">
                                    {{ $booking->booth->size === 'small' ? 'صغير' : '' }}
                                    {{ $booking->booth->size === 'medium' ? 'متوسط' : '' }}
                                    {{ $booking->booth->size === 'large' ? 'كبير' : '' }}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المساحة:</span>
                                <span class="font-medium">{{ $booking->booth->area }} متر مربع</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الموقع:</span>
                                <span class="font-medium">{{ $booking->booth->location }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Exhibition Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">📅 معلومات المعرض</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">اسم المعرض:</span>
                                <span class="font-medium">{{ $booking->exhibition->title }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">التاريخ:</span>
                                <span class="font-medium">{{ $booking->exhibition->start_date->format('d/m/Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المكان:</span>
                                <span class="font-medium">{{ $booking->exhibition->venue_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المدينة:</span>
                                <span class="font-medium">{{ $booking->exhibition->city }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 معلومات الدفع</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">سعر الجناح:</span>
                            <span class="font-medium">{{ number_format($booking->total_amount, 3) }} {{ $booking->currency }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الحجز:</span>
                            <span class="font-medium">{{ $booking->booking_date->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>

                    <div class="space-y-2">
                        @if($booking->payments->isNotEmpty())
                            @php $payment = $booking->payments->first() @endphp
                            <div class="flex justify-between">
                                <span class="text-gray-600">حالة الدفع:</span>
                                <span class="font-medium
                                    {{ $payment->status === 'pending' ? 'text-yellow-600' : '' }}
                                    {{ $payment->status === 'completed' ? 'text-green-600' : '' }}
                                    {{ $payment->status === 'failed' ? 'text-red-600' : '' }}">
                                    {{ $payment->status === 'pending' ? 'في انتظار الدفع' : '' }}
                                    {{ $payment->status === 'completed' ? 'مدفوع' : '' }}
                                    {{ $payment->status === 'failed' ? 'فشل الدفع' : '' }}
                                </span>
                            </div>
                            @if($payment->payment_date)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">تاريخ الدفع:</span>
                                    <span class="font-medium">{{ $payment->payment_date->format('d/m/Y H:i') }}</span>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>

                <!-- Payment Actions -->
                @if($booking->status === 'pending')
                    <div class="border-t pt-6">
                        <div class="text-center">
                            <p class="text-gray-600 mb-4">يرجى إكمال عملية الدفع لتأكيد حجز الجناح</p>
                            <!-- MyFatoorah Payment Button -->
                            <div class="space-y-4">
                                <form method="POST" action="/payment/initiate/{{ $booking->id }}" style="display: inline;">
                                    @csrf
                                    <button type="submit" class="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors w-full">
                                        💳 ادفع الآن عبر MyFatoorah - {{ number_format($booking->total_amount, 3) }} {{ $booking->currency }}
                                    </button>
                                </form>

                                <!-- Alternative: Manual Payment Confirmation (for testing) -->
                                <div class="border-t pt-4">
                                    <p class="text-sm text-gray-600 mb-2">أو في حالة مشاكل تقنية:</p>
                                    <form method="POST" action="/bookings/{{ $booking->id }}/confirm-payment" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors w-full">
                                            ✅ تأكيد الدفع يدوياً (للاختبار)
                                        </button>
                                    </form>
                                </div>

                                <p class="text-sm text-gray-500 mt-2">
                                    🔒 دفع آمن عبر بوابة MyFatoorah المعتمدة في الكويت
                                </p>
                            </div>
                        </div>
                    </div>
                @elseif($booking->status === 'confirmed')
                    <div class="border-t pt-6">
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <span class="text-green-600 text-2xl">✅</span>
                            <p class="text-green-800 font-semibold mt-2">تم تأكيد الحجز والدفع بنجاح!</p>
                            <p class="text-green-600 text-sm mt-1">سيتم إرسال تفاصيل الحجز إلى بريدك الإلكتروني</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Customer Information -->
            @if($booking->exhibitor_details)
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">👤 معلومات العارض</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if(isset($booking->exhibitor_details['company']))
                            <div class="flex justify-between">
                                <span class="text-gray-600">الشركة:</span>
                                <span class="font-medium">{{ $booking->exhibitor_details['company'] }}</span>
                            </div>
                        @endif
                        @if(isset($booking->exhibitor_details['contact_person']))
                            <div class="flex justify-between">
                                <span class="text-gray-600">الشخص المسؤول:</span>
                                <span class="font-medium">{{ $booking->exhibitor_details['contact_person'] }}</span>
                            </div>
                        @endif
                        @if(isset($booking->exhibitor_details['email']))
                            <div class="flex justify-between">
                                <span class="text-gray-600">البريد الإلكتروني:</span>
                                <span class="font-medium">{{ $booking->exhibitor_details['email'] }}</span>
                            </div>
                        @endif
                        @if(isset($booking->exhibitor_details['phone']))
                            <div class="flex justify-between">
                                <span class="text-gray-600">الهاتف:</span>
                                <span class="font-medium">{{ $booking->exhibitor_details['phone'] }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🔗 إجراءات سريعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="/dashboard" class="text-center bg-blue-100 text-blue-700 py-3 px-4 rounded-lg hover:bg-blue-200 transition-colors">
                        🏠 لوحة التحكم
                    </a>
                    <a href="/exhibitions/{{ $booking->exhibition->slug }}" class="text-center bg-purple-100 text-purple-700 py-3 px-4 rounded-lg hover:bg-purple-200 transition-colors">
                        📋 تفاصيل المعرض
                    </a>
                    <a href="/exhibitions/{{ $booking->exhibition->slug }}/booths" class="text-center bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors">
                        🏢 الأجنحة الأخرى
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500">
                <p>© 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
</body>
</html>
