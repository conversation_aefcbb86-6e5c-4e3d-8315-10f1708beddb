<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تفاصيل الحجز - Season Expo</title>

    <!-- Custom Favicon -->
    <link rel="icon" type="image/svg+xml" href="/season-expo-favicon.svg">
    <link rel="icon" type="image/x-icon" href="/season-expo-favicon.ico">
    <link rel="shortcut icon" href="/season-expo-favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Success/Error Messages -->
            <?php if(session('success')): ?>
                <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-2xl ml-3">✅</span>
                        <span><?php echo e(session('success')); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-2xl ml-3">❌</span>
                        <div>
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <p><?php echo e($error); ?></p>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Booking Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">تفاصيل الحجز</h1>
                        <p class="text-gray-600">رقم الحجز: #<?php echo e($booking->id); ?></p>
                    </div>
                    <span class="px-3 py-1 text-sm font-medium rounded-full
                        <?php echo e($booking->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                        <?php echo e($booking->status === 'confirmed' ? 'bg-green-100 text-green-800' : ''); ?>

                        <?php echo e($booking->status === 'paid' ? 'bg-green-100 text-green-800' : ''); ?>

                        <?php echo e($booking->status === 'cancelled' ? 'bg-red-100 text-red-800' : ''); ?>">
                        <?php echo e($booking->status === 'pending' ? 'في انتظار الدفع' : ''); ?>

                        <?php echo e($booking->status === 'confirmed' ? 'مؤكد' : ''); ?>

                        <?php echo e($booking->status === 'paid' ? 'مدفوع ✅' : ''); ?>

                        <?php echo e($booking->status === 'cancelled' ? 'ملغي' : ''); ?>

                    </span>
                </div>

                <!-- Booking Details Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Booth Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">🏢 معلومات الجناح</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">رقم الجناح:</span>
                                <span class="font-medium"><?php echo e($booking->booth->booth_number); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الحجم:</span>
                                <span class="font-medium">
                                    <?php echo e($booking->booth->size === 'small' ? 'صغير' : ''); ?>

                                    <?php echo e($booking->booth->size === 'medium' ? 'متوسط' : ''); ?>

                                    <?php echo e($booking->booth->size === 'large' ? 'كبير' : ''); ?>

                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المساحة:</span>
                                <span class="font-medium"><?php echo e($booking->booth->area); ?> متر مربع</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الموقع:</span>
                                <span class="font-medium"><?php echo e($booking->booth->location); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Exhibition Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">📅 معلومات المعرض</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">اسم المعرض:</span>
                                <span class="font-medium"><?php echo e($booking->exhibition->title); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">التاريخ:</span>
                                <span class="font-medium"><?php echo e($booking->exhibition->start_date->format('d/m/Y')); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المكان:</span>
                                <span class="font-medium"><?php echo e($booking->exhibition->venue_name); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المدينة:</span>
                                <span class="font-medium"><?php echo e($booking->exhibition->city); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Digital Signature Status -->
            <?php if($booking->signature_data): ?>
                <div class="bg-green-50 border border-green-200 rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-green-900 mb-4">✅ حالة الإقرار والتعهد</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-green-800"><strong>الحالة:</strong> تم التوقيع بنجاح</p>
                            <p class="text-green-800"><strong>تاريخ التوقيع:</strong> <?php echo e($booking->signature_date ? $booking->signature_date->format('d/m/Y H:i') : 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <?php if($booking->trade_name): ?>
                                <p class="text-green-800"><strong>الاسم التجاري:</strong> <?php echo e($booking->trade_name); ?></p>
                            <?php endif; ?>
                            <?php if($booking->license_holder_name): ?>
                                <p class="text-green-800"><strong>صاحب الترخيص:</strong> <?php echo e($booking->license_holder_name); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Payment Information -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 معلومات الدفع</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">سعر الجناح:</span>
                            <span class="font-medium"><?php echo e(number_format($booking->total_amount, 3)); ?> <?php echo e($booking->currency); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الحجز:</span>
                            <span class="font-medium"><?php echo e($booking->booking_date->format('d/m/Y H:i')); ?></span>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <?php if($booking->payments->isNotEmpty()): ?>
                            <?php $payment = $booking->payments->first() ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">حالة الدفع:</span>
                                <span class="font-medium
                                    <?php echo e($payment->status === 'pending' ? 'text-yellow-600' : ''); ?>

                                    <?php echo e($payment->status === 'completed' ? 'text-green-600' : ''); ?>

                                    <?php echo e($payment->status === 'failed' ? 'text-red-600' : ''); ?>">
                                    <?php echo e($payment->status === 'pending' ? 'في انتظار الدفع' : ''); ?>

                                    <?php echo e($payment->status === 'completed' ? 'مدفوع ✅' : ''); ?>

                                    <?php echo e($payment->status === 'failed' ? 'فشل الدفع ❌' : ''); ?>

                                </span>
                            </div>

                            <?php if($payment->status === 'completed'): ?>
                                <?php if($payment->paid_at): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">تاريخ الدفع:</span>
                                        <span class="font-medium"><?php echo e($payment->paid_at->format('d/m/Y H:i')); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if($payment->transaction_id): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">رقم المعاملة:</span>
                                        <span class="font-medium text-sm"><?php echo e($payment->transaction_id); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if($payment->payment_method): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">طريقة الدفع:</span>
                                        <span class="font-medium">
                                            <?php echo e($payment->payment_method === 'myfatoorah' ? 'MyFatoorah - K-Net' : $payment->payment_method); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if($payment->gateway): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">البوابة:</span>
                                        <span class="font-medium"><?php echo e($payment->gateway); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">حالة الدفع:</span>
                                <span class="font-medium text-yellow-600">لم يتم الدفع بعد</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Payment Actions -->
                <?php if($booking->status === 'pending'): ?>
                    <div class="border-t pt-6">
                        <div class="text-center">
                            <?php if($booking->signature_data): ?>
                                <!-- Signature completed - show payment options -->
                                <p class="text-gray-600 mb-4">تم إكمال الإقرار والتعهد. يرجى اختيار طريقة الدفع لتأكيد حجز الجناح</p>
                                <div class="space-y-4">
                                    <!-- MyFatoorah Payment Button -->
                                    <a href="/payment/initiate/<?php echo e($booking->id); ?>" class="block bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors w-full text-center">
                                        💳 ادفع الآن عبر MyFatoorah - <?php echo e(number_format($booking->total_amount, 3)); ?> <?php echo e($booking->currency); ?>

                                    </a>

                                    <!-- Cash Payment Button -->
                                    <form method="POST" action="/payment/cash/<?php echo e($booking->id); ?>" class="w-full">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="block bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors w-full text-center">
                                            💵 دفع نقدي - <?php echo e(number_format($booking->total_amount, 3)); ?> <?php echo e($booking->currency); ?>

                                        </button>
                                    </form>

                                    <p class="text-sm text-gray-500 mt-2">
                                        🔒 اختر طريقة الدفع المناسبة لك
                                    </p>
                                </div>
                            <?php else: ?>
                                <!-- Signature required first -->
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-4">
                                    <h4 class="font-bold text-yellow-900 mb-2">📋 مطلوب: إقرار وتعهد</h4>
                                    <p class="text-yellow-800 mb-4">
                                        يجب توقيع الإقرار والتعهد أولاً قبل إتمام عملية الدفع وفقاً للقرار الوزاري رقم (303) لسنة 2018
                                    </p>
                                    <a href="/digital-signature/<?php echo e($booking->id); ?>" class="block bg-yellow-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-yellow-700 transition-colors w-full text-center">
                                        ✍️ توقيع الإقرار والتعهد
                                    </a>
                                </div>

                                <p class="text-sm text-gray-500 text-center">
                                    بعد إكمال الإقرار والتعهد، ستتمكن من اختيار طريقة الدفع
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php elseif($booking->status === 'paid' || $booking->status === 'confirmed'): ?>
                    <div class="border-t pt-6">
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <span class="text-green-600 text-2xl">✅</span>
                            <p class="text-green-800 font-semibold mt-2">تم تأكيد الحجز والدفع بنجاح!</p>
                            <p class="text-green-600 text-sm mt-1">سيتم إرسال تفاصيل الحجز إلى بريدك الإلكتروني</p>

                            <?php if($booking->payments->isNotEmpty() && $booking->payments->first()->status === 'completed'): ?>
                                <div class="mt-4 p-3 bg-white rounded border">
                                    <p class="text-sm text-gray-600">✅ تم الدفع بنجاح عبر MyFatoorah</p>
                                    <?php if($booking->payments->first()->paid_at): ?>
                                        <p class="text-xs text-gray-500"><?php echo e($booking->payments->first()->paid_at->format('d/m/Y H:i')); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Customer Information -->
            <?php if($booking->exhibitor_details): ?>
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">👤 معلومات العارض</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php if(isset($booking->exhibitor_details['company'])): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الشركة:</span>
                                <span class="font-medium"><?php echo e($booking->exhibitor_details['company']); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if(isset($booking->exhibitor_details['contact_person'])): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الشخص المسؤول:</span>
                                <span class="font-medium"><?php echo e($booking->exhibitor_details['contact_person']); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if(isset($booking->exhibitor_details['email'])): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">البريد الإلكتروني:</span>
                                <span class="font-medium"><?php echo e($booking->exhibitor_details['email']); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if(isset($booking->exhibitor_details['phone'])): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الهاتف:</span>
                                <span class="font-medium"><?php echo e($booking->exhibitor_details['phone']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🔗 إجراءات سريعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="/dashboard" class="text-center bg-blue-100 text-blue-700 py-3 px-4 rounded-lg hover:bg-blue-200 transition-colors">
                        🏠 حسابي
                    </a>
                    <a href="/exhibitions/<?php echo e($booking->exhibition->slug); ?>" class="text-center bg-purple-100 text-purple-700 py-3 px-4 rounded-lg hover:bg-purple-200 transition-colors">
                        📋 تفاصيل المعرض
                    </a>
                    <a href="/exhibitions/<?php echo e($booking->exhibition->slug); ?>/booths" class="text-center bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors">
                        🏢 الأجنحة الأخرى
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500">
                <p>© 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/bookings/show-simple.blade.php ENDPATH**/ ?>