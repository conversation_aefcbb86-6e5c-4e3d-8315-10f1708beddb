<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookingSignatureController extends Controller
{
    /**
     * Show the digital signature form for a booking.
     */
    public function show($bookingId)
    {
        $booking = Booking::with(['exhibition', 'user', 'booth'])
                         ->where('id', $bookingId)
                         ->where('user_id', Auth::id())
                         ->firstOrFail();

        // Allow signature for pending bookings (before payment)
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('error', 'لا يمكن التوقيع على هذا الحجز في حالته الحالية.');
        }

        // Check if already signed
        if ($booking->signature_data) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('info', 'تم التوقيع الرقمي مسبقاً. يمكنك اختيار طريقة الدفع.');
        }

        $user = Auth::user();
        return view('digital-signature.form', compact('booking', 'user'));
    }

    /**
     * Store the digital signature for a booking.
     */
    public function store(Request $request, $bookingId)
    {
        $request->validate([
            'trade_name' => 'required|string|max:255',
            'license_holder_name' => 'required|string|max:255',
            'commercial_license_number' => 'required|string|max:100',
            'license_date' => 'required|date',
            'signature_agreement' => 'required|accepted',
            'signature' => 'required|string',
        ]);

        $booking = Booking::where('id', $bookingId)
                         ->where('user_id', Auth::id())
                         ->firstOrFail();

        // Allow signature for pending bookings (before payment)
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return redirect()->route('bookings.show', $booking->id)
                           ->with('error', 'لا يمكن التوقيع على هذا الحجز في حالته الحالية.');
        }

        // Store signature data and company information
        $booking->update([
            'signature_data' => $request->signature,
            'signature_date' => now(),
            'signature_ip' => $request->ip(),
            'signature_user_agent' => $request->userAgent(),
            'trade_name' => $request->trade_name,
            'license_holder_name' => $request->license_holder_name,
            'commercial_license_number' => $request->commercial_license_number,
            'license_date' => $request->license_date,
        ]);

        // Redirect back to booking page to show payment options
        return redirect()->route('bookings.show', $booking->id)
                        ->with('success', 'تم حفظ الإقرار والتعهد والتوقيع الرقمي بنجاح. يمكنك الآن اختيار طريقة الدفع.');
    }
}
