/* Print Styles for Season Expo Platform */

@media print {
    /* Hide navigation and unnecessary elements */
    nav, .nav, .navbar,
    .sidebar, .menu,
    .btn, button,
    .no-print,
    .fixed, .sticky {
        display: none !important;
    }

    /* Ensure body takes full width */
    body {
        margin: 0;
        padding: 20px;
        font-family: 'Arial', sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
    }

    /* Print header with logo */
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #333;
        page-break-inside: avoid;
    }

    /* Auto-generated print header */
    body::before {
        content: "";
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 100px;
        background: white;
        background-image: url('/images/logo.png');
        background-repeat: no-repeat;
        background-position: center 20px;
        background-size: auto 60px;
        border-bottom: 2px solid #333;
        z-index: 1000;
    }

    .print-logo {
        max-height: 60px;
        width: auto;
        margin-bottom: 10px;
    }

    .print-title {
        font-size: 18pt;
        font-weight: bold;
        margin: 10px 0;
        color: #333;
    }

    .print-subtitle {
        font-size: 12pt;
        color: #666;
        margin: 5px 0;
    }

    /* Print footer with logo */
    .print-footer {
        display: block !important;
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ccc;
        font-size: 10pt;
        color: #666;
        page-break-inside: avoid;
    }

    /* Auto-generated print footer */
    body::after {
        content: "© 2024 جميع الحقوق محفوظة";
        display: block;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: white;
        background-image: url('/images/logo-small.png');
        background-repeat: no-repeat;
        background-position: center 10px;
        background-size: auto 30px;
        border-top: 1px solid #ccc;
        text-align: center;
        padding-top: 40px;
        font-size: 10pt;
        color: #666;
        z-index: 1000;
    }

    .print-footer-logo {
        max-height: 30px;
        width: auto;
        margin-bottom: 10px;
    }

    /* Content styling */
    .print-content {
        margin: 120px 0 80px 0; /* Space for header and footer */
        padding: 0 20px;
    }

    /* Ensure content doesn't overlap with header/footer */
    body {
        margin-top: 120px !important;
        margin-bottom: 80px !important;
    }

    /* Table styling for print */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }

    table, th, td {
        border: 1px solid #333;
    }

    th, td {
        padding: 8px;
        text-align: right;
    }

    th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    /* Page breaks */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* Headings */
    h1, h2, h3, h4, h5, h6 {
        color: #333;
        margin: 20px 0 10px 0;
        page-break-after: avoid;
    }

    h1 {
        font-size: 18pt;
        border-bottom: 2px solid #333;
        padding-bottom: 5px;
    }

    h2 {
        font-size: 16pt;
    }

    h3 {
        font-size: 14pt;
    }

    /* Links */
    a {
        color: #000;
        text-decoration: none;
    }

    a:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }

    /* Images */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Specific print sections */
    .exhibition-details,
    .booth-details,
    .booking-details {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }

    /* QR codes and barcodes */
    .qr-code,
    .barcode {
        text-align: center;
        margin: 20px 0;
    }

    /* Signatures */
    .signature-section {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #ccc;
    }

    .signature-line {
        border-bottom: 1px solid #333;
        width: 200px;
        height: 40px;
        margin: 20px 0;
        display: inline-block;
    }

    /* Date and time */
    .print-date {
        text-align: left;
        font-size: 10pt;
        color: #666;
        margin-bottom: 20px;
    }

    /* Arabic text support */
    .arabic {
        direction: rtl;
        text-align: right;
    }

    /* Hide elements that shouldn't be printed */
    .screen-only {
        display: none !important;
    }

    /* Show elements only in print */
    .print-only {
        display: block !important;
    }

    /* Contract and agreement styling */
    .contract-section {
        margin: 30px 0;
        padding: 20px;
        border: 2px solid #333;
        page-break-inside: avoid;
    }

    .contract-title {
        font-size: 16pt;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
        text-decoration: underline;
    }

    .contract-clause {
        margin: 15px 0;
        padding: 10px;
        background-color: #f9f9f9;
    }

    /* Invoice styling */
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .invoice-number {
        font-size: 14pt;
        font-weight: bold;
        color: #333;
    }

    .invoice-total {
        font-size: 16pt;
        font-weight: bold;
        text-align: center;
        margin: 20px 0;
        padding: 15px;
        border: 2px solid #333;
        background-color: #f0f0f0;
    }
}

/* Print button styling (visible on screen) */
@media screen {
    .print-button {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1000;
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .print-button:hover {
        background: #0056b3;
    }

    /* Hide print-only elements on screen */
    .print-only {
        display: none;
    }
}
