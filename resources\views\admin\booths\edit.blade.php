<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تعديل جناح {{ $booth->booth_number ?? 'الجناح' }} - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                    <span class="mr-4 px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded">Admin</span>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/admin/exhibitions" class="text-gray-600 hover:text-gray-900">إدارة المعارض</a>
                    <a href="/admin/exhibitions/{{ $booth->exhibition_id ?? 1 }}/booths" class="text-blue-600 hover:text-blue-700">إدارة الأجنحة</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">تعديل الجناح</h1>
                        <p class="text-gray-600 mt-2">جناح {{ $booth->booth_number ?? 'A-1' }} - {{ $booth->exhibition->title ?? 'معرض التكنولوجيا' }}</p>
                    </div>
                    <a href="/admin/exhibitions/{{ $booth->exhibition_id ?? 1 }}/booths" class="text-blue-600 hover:text-blue-700">← العودة للأجنحة</a>
                </div>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-green-600 text-xl ml-3">✅</span>
                        <p class="text-green-800 font-semibold">{{ session('success') }}</p>
                    </div>
                </div>
            @endif

            @if($errors->any())
                <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-red-600 text-xl ml-3">❌</span>
                        <div>
                            <p class="text-red-800 font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                            <ul class="text-red-700 list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Booth Info Card -->
            <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-blue-900 mb-4">معلومات المعرض</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <div class="text-sm text-blue-600 font-medium">اسم المعرض</div>
                        <div class="text-blue-900">{{ $booth->exhibition->title ?? 'معرض التكنولوجيا والابتكار 2024' }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">الموقع</div>
                        <div class="text-blue-900">{{ $booth->exhibition->location ?? 'مركز الكويت الدولي للمعارض' }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">حالة المعرض</div>
                        <div class="text-blue-900">{{ $booth->exhibition->status ?? 'نشط' }}</div>
                    </div>
                </div>
            </div>

            <!-- Edit Form -->
            <form method="POST" action="/admin/booths/{{ $booth->id ?? 1 }}" class="space-y-8">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">معلومات الجناح الأساسية</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Booth Number -->
                        <div>
                            <label for="booth_number" class="block text-sm font-medium text-gray-700 mb-2">رقم الجناح</label>
                            <input type="text" id="booth_number" name="booth_number" value="{{ old('booth_number', $booth->booth_number ?? 'A-1') }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="مثال: A-1, B-15">
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">حالة الجناح</label>
                            <select id="status" name="status" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="available" {{ old('status', $booth->status ?? 'available') == 'available' ? 'selected' : '' }}>متاح</option>
                                <option value="booked" {{ old('status', $booth->status ?? 'available') == 'booked' ? 'selected' : '' }}>محجوز</option>
                                <option value="maintenance" {{ old('status', $booth->status ?? 'available') == 'maintenance' ? 'selected' : '' }}>صيانة</option>
                            </select>
                        </div>

                        <!-- Size -->
                        <div>
                            <label for="size" class="block text-sm font-medium text-gray-700 mb-2">المساحة (متر مربع)</label>
                            <input type="number" id="size" name="size" value="{{ old('size', $booth->size ?? 25) }}" min="1" step="0.1" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="25">
                        </div>

                        <!-- Price -->
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">السعر (د.ك)</label>
                            <input type="number" id="price" name="price" value="{{ old('price', $booth->price ?? 500) }}" min="0" step="0.01" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="500.00">
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="mt-6">
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">الموقع داخل المعرض</label>
                        <input type="text" id="location" name="location" value="{{ old('location', $booth->location ?? 'القاعة الرئيسية') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="مثال: القاعة الرئيسية، الجناح الشرقي">
                    </div>

                    <!-- Description -->
                    <div class="mt-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">وصف الجناح (اختياري)</label>
                        <textarea id="description" name="description" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="وصف إضافي للجناح، المميزات، الملاحظات...">{{ old('description', $booth->description ?? '') }}</textarea>
                    </div>
                </div>

                <!-- Current Booking Info (if booked) -->
                @if(($booth->status ?? 'available') === 'booked')
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-yellow-900 mb-4">معلومات الحجز الحالي</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div class="text-sm text-yellow-600 font-medium">العارض</div>
                            <div class="text-yellow-900">شركة التكنولوجيا المتقدمة</div>
                        </div>
                        <div>
                            <div class="text-sm text-yellow-600 font-medium">تاريخ الحجز</div>
                            <div class="text-yellow-900">2024-01-15</div>
                        </div>
                        <div>
                            <div class="text-sm text-yellow-600 font-medium">حالة الدفع</div>
                            <div class="text-yellow-900">مدفوع</div>
                        </div>
                        <div>
                            <div class="text-sm text-yellow-600 font-medium">المبلغ المدفوع</div>
                            <div class="text-yellow-900">{{ $booth->price ?? 500 }} د.ك</div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="/admin/booths/{{ $booth->id ?? 1 }}/booking" class="text-yellow-700 hover:text-yellow-800 font-medium">
                            عرض تفاصيل الحجز كاملة →
                        </a>
                    </div>
                </div>
                @endif

                <!-- Action Buttons -->
                <div class="flex justify-between items-center">
                    <div class="flex gap-4">
                        <a href="/admin/exhibitions/{{ $booth->exhibition_id ?? 1 }}/booths"
                           class="px-6 py-3 bg-gray-500 text-white rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                            إلغاء
                        </a>
                        <button type="submit"
                                class="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            حفظ التغييرات
                        </button>
                    </div>

                    <!-- Delete Button -->
                    <button type="button" onclick="deleteBooth()"
                            class="px-6 py-3 bg-red-600 text-white rounded-lg font-semibold hover:bg-red-700 transition-colors">
                        حذف الجناح
                    </button>
                </div>
            </form>

            <!-- Booth Statistics -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-lg font-bold text-gray-900">{{ $booth->size ?? 25 }} م²</div>
                            <div class="text-sm text-gray-500">مساحة الجناح</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">💰</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-lg font-bold text-gray-900">{{ $booth->price ?? 500 }} د.ك</div>
                            <div class="text-sm text-gray-500">سعر الجناح</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">
                                {{ ($booth->status ?? 'available') === 'available' ? '✅' :
                                   (($booth->status ?? 'available') === 'booked' ? '📋' : '🔧') }}
                            </span>
                        </div>
                        <div class="mr-4">
                            <div class="text-lg font-bold text-gray-900">
                                {{ ($booth->status ?? 'available') === 'available' ? 'متاح' :
                                   (($booth->status ?? 'available') === 'booked' ? 'محجوز' : 'صيانة') }}
                            </div>
                            <div class="text-sm text-gray-500">حالة الجناح</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Script -->
    <script>
        function deleteBooth() {
            if (confirm('هل أنت متأكد من حذف هذا الجناح؟ سيتم حذف جميع الحجوزات المرتبطة به.')) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/booths/{{ $booth->id ?? 1 }}';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
