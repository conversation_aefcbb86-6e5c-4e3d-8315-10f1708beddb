<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('البحث عن الأجنحة') }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        body {
            font-family: {{ app()->getLocale() == 'ar' ? "'Noto Sans Arabic', sans-serif" : "'Figtree', sans-serif" }};
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="/" class="flex-shrink-0">
                            <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                            <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            {{ __('حسابي') }}
                        </a>
                        <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            {{ __('حجوزاتي') }}
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('البحث عن الأجنحة') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('ابحث عن الجناح المثالي لمعرضك') }}</p>
                </div>

                <!-- Search Filters -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                    <form method="GET" action="{{ route('dashboard.booth-search') }}" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Exhibition Filter -->
                            <div>
                                <label for="exhibition_id" class="block text-sm font-medium text-gray-700 mb-2">{{ __('المعرض') }}</label>
                                <select name="exhibition_id" id="exhibition_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">{{ __('جميع المعارض') }}</option>
                                    @foreach($exhibitions as $exhibition)
                                        <option value="{{ $exhibition->id }}" {{ request('exhibition_id') == $exhibition->id ? 'selected' : '' }}>
                                            {{ $exhibition->title }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Price Range -->
                            <div>
                                <label for="min_price" class="block text-sm font-medium text-gray-700 mb-2">{{ __('السعر الأدنى') }}</label>
                                <input type="number" name="min_price" id="min_price" value="{{ request('min_price') }}" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="0">
                            </div>

                            <div>
                                <label for="max_price" class="block text-sm font-medium text-gray-700 mb-2">{{ __('السعر الأعلى') }}</label>
                                <input type="number" name="max_price" id="max_price" value="{{ request('max_price') }}" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="10000">
                            </div>

                            <!-- Size Filter -->
                            <div>
                                <label for="size" class="block text-sm font-medium text-gray-700 mb-2">{{ __('الحجم') }}</label>
                                <select name="size" id="size" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">{{ __('جميع الأحجام') }}</option>
                                    <option value="small" {{ request('size') == 'small' ? 'selected' : '' }}>{{ __('صغير') }}</option>
                                    <option value="medium" {{ request('size') == 'medium' ? 'selected' : '' }}>{{ __('متوسط') }}</option>
                                    <option value="large" {{ request('size') == 'large' ? 'selected' : '' }}>{{ __('كبير') }}</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex justify-between items-center pt-4">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                                🔍 {{ __('بحث') }}
                            </button>
                            
                            @if(request()->hasAny(['exhibition_id', 'min_price', 'max_price', 'size']))
                                <a href="{{ route('dashboard.booth-search') }}" class="text-gray-600 hover:text-gray-900">
                                    {{ __('مسح المرشحات') }}
                                </a>
                            @endif
                        </div>
                    </form>
                </div>

                <!-- Results -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <!-- Results Header -->
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900">
                                {{ __('الأجنحة المتاحة') }} ({{ $booths->total() }})
                            </h2>
                            
                            <!-- Print Button -->
                            <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                🖨️ {{ __('طباعة') }}
                            </button>
                        </div>
                    </div>

                    <!-- Booths Grid -->
                    @if($booths->count() > 0)
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($booths as $booth)
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <!-- Booth Header -->
                                        <div class="flex justify-between items-start mb-3">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">{{ __('جناح رقم') }} {{ $booth->booth_number }}</h3>
                                                <p class="text-sm text-gray-600">{{ $booth->exhibition->title ?? __('غير محدد') }}</p>
                                            </div>
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                                {{ __('متاح') }}
                                            </span>
                                        </div>

                                        <!-- Booth Details -->
                                        <div class="space-y-2 mb-4">
                                            @if($booth->size)
                                                <div class="flex justify-between text-sm">
                                                    <span class="text-gray-600">{{ __('الحجم') }}:</span>
                                                    <span class="font-medium">
                                                        @if($booth->size == 'small') {{ __('صغير') }}
                                                        @elseif($booth->size == 'medium') {{ __('متوسط') }}
                                                        @elseif($booth->size == 'large') {{ __('كبير') }}
                                                        @else {{ $booth->size }}
                                                        @endif
                                                    </span>
                                                </div>
                                            @endif

                                            @if($booth->area)
                                                <div class="flex justify-between text-sm">
                                                    <span class="text-gray-600">{{ __('المساحة') }}:</span>
                                                    <span class="font-medium">{{ $booth->area }} {{ __('م²') }}</span>
                                                </div>
                                            @endif

                                            @if($booth->price)
                                                <div class="flex justify-between text-sm">
                                                    <span class="text-gray-600">{{ __('السعر') }}:</span>
                                                    <span class="font-medium text-green-600">{{ number_format($booth->price) }} {{ __('د.ك') }}</span>
                                                </div>
                                            @endif

                                            @if($booth->location)
                                                <div class="flex justify-between text-sm">
                                                    <span class="text-gray-600">{{ __('الموقع') }}:</span>
                                                    <span class="font-medium">{{ $booth->location }}</span>
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex space-x-2 rtl:space-x-reverse">
                                            <a href="/exhibitions/{{ $booth->exhibition_id }}/booths/{{ $booth->id }}"
                                               class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-center text-sm font-medium">
                                                {{ __('عرض التفاصيل') }}
                                            </a>

                                            @auth
                                                <form method="POST" action="/exhibitions/{{ $booth->exhibition_id }}/booths/{{ $booth->id }}/book" class="flex-1">
                                                    @csrf
                                                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-center text-sm font-medium"
                                                            onclick="this.textContent='جاري الحجز...'; this.disabled=true; this.form.submit();">
                                                        {{ __('احجز الآن') }}
                                                    </button>
                                                </form>
                                            @else
                                                <a href="/login" class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-center text-sm font-medium">
                                                    {{ __('سجل دخول للحجز') }}
                                                </a>
                                            @endauth
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Pagination -->
                            @if($booths->hasPages())
                                <div class="mt-8">
                                    {{ $booths->appends(request()->query())->links() }}
                                </div>
                            @endif
                        </div>
                    @else
                        <!-- No Results -->
                        <div class="p-12 text-center">
                            <div class="text-gray-400 text-6xl mb-4">🏢</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('لا توجد أجنحة متاحة') }}</h3>
                            <p class="text-gray-600 mb-4">{{ __('جرب تعديل معايير البحث للعثور على الأجنحة المتاحة') }}</p>
                            
                            @if(request()->hasAny(['exhibition_id', 'min_price', 'max_price', 'size']))
                                <a href="{{ route('dashboard.booth-search') }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                                    {{ __('مسح جميع المرشحات') }}
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Print Styles -->
    <style media="print">
        nav, .no-print { display: none !important; }
        body { background: white !important; }
        .shadow-sm, .shadow-md { box-shadow: none !important; }
        .border { border: 1px solid #ccc !important; }
    </style>
</body>
</html>
