<?php
    $isArabic = ($locale ?? 'ar') === 'ar';
?>
<!DOCTYPE html>
<html lang="<?php echo e($isArabic ? 'ar' : 'en'); ?>" dir="<?php echo e($isArabic ? 'rtl' : 'ltr'); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($isArabic ? 'اتصل بنا - منصة المعارض الرائدة' : 'Contact Us - Leading Exhibition Platform'); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    <link rel="manifest" href="/images/site.webmanifest">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            padding-top: 64px;
        }
        .hero-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .hero-with-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                        url('https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80') center/cover,
                        url('/images/contact-bg.jpg') center/cover;
            background-attachment: fixed;
            position: relative;
            min-height: 400px;
        }
        .hero-with-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
            z-index: 1;
        }
        .hero-content {
            position: relative;
            z-index: 2;
        }
        .card-hover { transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }
        .contact-form { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }
        .text-shadow-lg { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'الصفحة الرئيسية' : 'Home'); ?></a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'المعارض' : 'Exhibitions'); ?></a>
                    <a href="/about" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'من نحن' : 'About Us'); ?></a>
                    <a href="/contact" class="text-blue-600 font-semibold"><?php echo e($isArabic ? 'اتصل بنا' : 'Contact Us'); ?></a>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="/dashboard" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'حسابي' : 'My Account'); ?></a>
                    <?php else: ?>
                        <a href="/login-simple" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'تسجيل الدخول' : 'Login'); ?></a>
                        <a href="/register-simple" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"><?php echo e($isArabic ? 'إنشاء حساب' : 'Sign Up'); ?></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Success Message -->
    <?php if(session('success')): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mx-4 mt-4 mb-6 relative" role="alert">
            <div class="flex items-center">
                <span class="text-2xl ml-3">✅</span>
                <div>
                    <strong class="font-bold"><?php echo e($isArabic ? 'تم الإرسال بنجاح!' : 'Message Sent Successfully!'); ?></strong>
                    <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                </div>
                <button onclick="this.parentElement.parentElement.style.display='none'" class="absolute top-0 bottom-0 left-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <title><?php echo e($isArabic ? 'إغلاق' : 'Close'); ?></title>
                        <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                    </svg>
                </button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Hero Section -->
    <section class="hero-with-bg text-white py-24">
        <div class="hero-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-8 text-shadow-lg">
                <?php echo e($isArabic ? 'اتصل بنا' : 'Contact Us'); ?>

            </h1>
            <p class="text-xl md:text-2xl text-white max-w-4xl mx-auto leading-relaxed font-medium">
                <?php echo e($isArabic ? 'نحن هنا لمساعدتك! تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك' : 'We\'re here to help! Contact us anytime and we\'ll be happy to answer your questions'); ?>

            </p>
            <div class="mt-10">
                <div class="inline-flex items-center bg-white bg-opacity-90 backdrop-blur-md rounded-full px-6 py-3 text-gray-800 border border-white border-opacity-50 shadow-xl">
                    <span class="text-2xl ml-3">📞</span>
                    <span class="font-semibold"><?php echo e($isArabic ? 'نحن متاحون 24/7 لخدمتك' : 'We\'re Available 24/7 to Serve You'); ?></span>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Information Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    <?php echo e($isArabic ? 'معلومات الاتصال' : 'Contact Information'); ?>

                </h2>
                <p class="text-xl text-gray-600">
                    <?php echo e($isArabic ? 'تواصل معنا عبر الطرق التالية' : 'Get in touch with us through the following methods'); ?>

                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Phone -->
                <div class="bg-blue-50 p-8 rounded-lg text-center card-hover">
                    <div class="text-4xl mb-4">📞</div>
                    <h3 class="text-xl font-semibold text-blue-900 mb-3">
                        <?php echo e($isArabic ? 'الهاتف' : 'Phone'); ?>

                    </h3>
                    <p class="text-blue-800 font-medium">+965 2222 3333</p>
                    <p class="text-blue-600 text-sm mt-2"><?php echo e($isArabic ? 'متاح 24/7' : 'Available 24/7'); ?></p>
                </div>

                <!-- Email -->
                <div class="bg-green-50 p-8 rounded-lg text-center card-hover">
                    <div class="text-4xl mb-4">📧</div>
                    <h3 class="text-xl font-semibold text-green-900 mb-3">
                        <?php echo e($isArabic ? 'البريد الإلكتروني' : 'Email'); ?>

                    </h3>
                    <p class="text-green-800 font-medium"><EMAIL></p>
                    <p class="text-green-600 text-sm mt-2"><?php echo e($isArabic ? 'رد خلال 24 ساعة' : 'Response within 24 hours'); ?></p>
                </div>

                <!-- Address -->
                <div class="bg-purple-50 p-8 rounded-lg text-center card-hover">
                    <div class="text-4xl mb-4">📍</div>
                    <h3 class="text-xl font-semibold text-purple-900 mb-3">
                        <?php echo e($isArabic ? 'العنوان' : 'Address'); ?>

                    </h3>
                    <p class="text-purple-800 font-medium"><?php echo e($isArabic ? 'مدينة الكويت' : 'Kuwait City'); ?></p>
                    <p class="text-purple-600 text-sm mt-2"><?php echo e($isArabic ? 'الكويت' : 'Kuwait'); ?></p>
                </div>

                <!-- WhatsApp -->
                <div class="bg-yellow-50 p-8 rounded-lg text-center card-hover">
                    <div class="text-4xl mb-4">💬</div>
                    <h3 class="text-xl font-semibold text-yellow-900 mb-3">
                        <?php echo e($isArabic ? 'واتساب' : 'WhatsApp'); ?>

                    </h3>
                    <p class="text-yellow-800 font-medium">+965 5555 6666</p>
                    <p class="text-yellow-600 text-sm mt-2"><?php echo e($isArabic ? 'دردشة فورية' : 'Instant Chat'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="py-20 contact-form">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
                <!-- Contact Form -->
                <div class="bg-white rounded-lg shadow-xl p-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">
                        <?php echo e($isArabic ? 'أرسل لنا رسالة' : 'Send Us a Message'); ?>

                    </h2>
                    <p class="text-gray-600 mb-8">
                        <?php echo e($isArabic ? 'املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن' : 'Fill out the form below and we\'ll get back to you as soon as possible'); ?>

                    </p>

                    <form method="POST" action="/contact" class="space-y-6">
                        <?php echo csrf_field(); ?>

                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo e($isArabic ? 'الاسم الكامل' : 'Full Name'); ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="name" name="name" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<?php echo e($isArabic ? 'أدخل اسمك الكامل' : 'Enter your full name'); ?>">
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-2 text-sm text-red-600"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo e($isArabic ? 'البريد الإلكتروني' : 'Email Address'); ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="email" name="email" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<?php echo e($isArabic ? '<EMAIL>' : '<EMAIL>'); ?>">
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-2 text-sm text-red-600"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo e($isArabic ? 'رقم الهاتف' : 'Phone Number'); ?>

                            </label>
                            <input type="tel" id="phone" name="phone"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<?php echo e($isArabic ? '+965 1234 5678' : '+965 1234 5678'); ?>">
                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-2 text-sm text-red-600"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo e($isArabic ? 'الموضوع' : 'Subject'); ?> <span class="text-red-500">*</span>
                            </label>
                            <select id="subject" name="subject" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value=""><?php echo e($isArabic ? 'اختر الموضوع' : 'Select Subject'); ?></option>
                                <option value="general"><?php echo e($isArabic ? 'استفسار عام' : 'General Inquiry'); ?></option>
                                <option value="booking"><?php echo e($isArabic ? 'حجز جناح' : 'Booth Booking'); ?></option>
                                <option value="exhibition"><?php echo e($isArabic ? 'تنظيم معرض' : 'Exhibition Organization'); ?></option>
                                <option value="technical"><?php echo e($isArabic ? 'دعم تقني' : 'Technical Support'); ?></option>
                                <option value="partnership"><?php echo e($isArabic ? 'شراكة' : 'Partnership'); ?></option>
                                <option value="other"><?php echo e($isArabic ? 'أخرى' : 'Other'); ?></option>
                            </select>
                            <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-2 text-sm text-red-600"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Message -->
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo e($isArabic ? 'الرسالة' : 'Message'); ?> <span class="text-red-500">*</span>
                            </label>
                            <textarea id="message" name="message" rows="5" required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="<?php echo e($isArabic ? 'اكتب رسالتك هنا...' : 'Write your message here...'); ?>"></textarea>
                            <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-2 text-sm text-red-600"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <?php echo e($isArabic ? 'إرسال الرسالة' : 'Send Message'); ?>

                        </button>
                    </form>
                </div>

                <!-- Contact Info & Map -->
                <div class="space-y-8">
                    <!-- Office Hours -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">
                            <?php echo e($isArabic ? 'ساعات العمل' : 'Office Hours'); ?>

                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600"><?php echo e($isArabic ? 'الأحد - الخميس' : 'Sunday - Thursday'); ?></span>
                                <span class="font-semibold text-gray-900">8:00 AM - 6:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600"><?php echo e($isArabic ? 'الجمعة' : 'Friday'); ?></span>
                                <span class="font-semibold text-gray-900">2:00 PM - 6:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600"><?php echo e($isArabic ? 'السبت' : 'Saturday'); ?></span>
                                <span class="font-semibold text-red-600"><?php echo e($isArabic ? 'مغلق' : 'Closed'); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">
                            <?php echo e($isArabic ? 'روابط سريعة' : 'Quick Links'); ?>

                        </h3>
                        <div class="space-y-3">
                            <a href="/exhibitions" class="block text-blue-600 hover:text-blue-800 font-medium">
                                <?php echo e($isArabic ? '📋 تصفح المعارض' : '📋 Browse Exhibitions'); ?>

                            </a>
                            <a href="/register-simple" class="block text-blue-600 hover:text-blue-800 font-medium">
                                <?php echo e($isArabic ? '👤 إنشاء حساب' : '👤 Create Account'); ?>

                            </a>
                            <a href="/about" class="block text-blue-600 hover:text-blue-800 font-medium">
                                <?php echo e($isArabic ? 'ℹ️ من نحن' : 'ℹ️ About Us'); ?>

                            </a>
                            <a href="/faq" class="block text-blue-600 hover:text-blue-800 font-medium">
                                <?php echo e($isArabic ? '❓ الأسئلة الشائعة' : '❓ FAQ'); ?>

                            </a>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">
                            <?php echo e($isArabic ? 'تابعنا على' : 'Follow Us'); ?>

                        </h3>
                        <div class="flex space-x-reverse space-x-4">
                            <a href="#" class="bg-blue-600 text-white p-3 rounded-full hover:bg-blue-700 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                            <a href="#" class="bg-blue-800 text-white p-3 rounded-full hover:bg-blue-900 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                                </svg>
                            </a>
                            <a href="#" class="bg-pink-600 text-white p-3 rounded-full hover:bg-pink-700 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                                </svg>
                            </a>
                            <a href="#" class="bg-red-600 text-white p-3 rounded-full hover:bg-red-700 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    <?php echo e($isArabic ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'); ?>

                </h2>
                <p class="text-xl text-gray-600">
                    <?php echo e($isArabic ? 'إجابات على أكثر الأسئلة شيوعاً' : 'Answers to the most common questions'); ?>

                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            <?php echo e($isArabic ? 'كيف يمكنني حجز جناح؟' : 'How can I book a booth?'); ?>

                        </h3>
                        <p class="text-gray-600">
                            <?php echo e($isArabic ? 'يمكنك تصفح المعارض المتاحة واختيار الجناح المناسب، ثم إكمال عملية الحجز والدفع عبر المنصة.' : 'You can browse available exhibitions, choose a suitable booth, then complete the booking and payment process through the platform.'); ?>

                        </p>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            <?php echo e($isArabic ? 'ما هي طرق الدفع المتاحة؟' : 'What payment methods are available?'); ?>

                        </h3>
                        <p class="text-gray-600">
                            <?php echo e($isArabic ? 'نقبل الدفع عبر بطاقات الائتمان، التحويل البنكي، والدفع النقدي في المكتب.' : 'We accept payment via credit cards, bank transfer, and cash payment at the office.'); ?>

                        </p>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            <?php echo e($isArabic ? 'هل يمكنني إلغاء الحجز؟' : 'Can I cancel my booking?'); ?>

                        </h3>
                        <p class="text-gray-600">
                            <?php echo e($isArabic ? 'نعم، يمكنك إلغاء الحجز حسب شروط الإلغاء المحددة لكل معرض.' : 'Yes, you can cancel your booking according to the cancellation terms specified for each exhibition.'); ?>

                        </p>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            <?php echo e($isArabic ? 'كيف يمكنني تنظيم معرض؟' : 'How can I organize an exhibition?'); ?>

                        </h3>
                        <p class="text-gray-600">
                            <?php echo e($isArabic ? 'تواصل معنا عبر النموذج أعلاه أو الهاتف لمناقشة متطلباتك وسنساعدك في تنظيم معرضك.' : 'Contact us via the form above or phone to discuss your requirements and we\'ll help you organize your exhibition.'); ?>

                        </p>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            <?php echo e($isArabic ? 'هل تقدمون خدمات إضافية؟' : 'Do you provide additional services?'); ?>

                        </h3>
                        <p class="text-gray-600">
                            <?php echo e($isArabic ? 'نعم، نقدم خدمات التسويق، التصميم، والدعم اللوجستي للمعارض.' : 'Yes, we provide marketing, design, and logistical support services for exhibitions.'); ?>

                        </p>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            <?php echo e($isArabic ? 'كيف يمكنني الحصول على الدعم؟' : 'How can I get support?'); ?>

                        </h3>
                        <p class="text-gray-600">
                            <?php echo e($isArabic ? 'فريق الدعم متاح 24/7 عبر الهاتف، البريد الإلكتروني، أو الدردشة المباشرة.' : 'Our support team is available 24/7 via phone, email, or live chat.'); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-8">
                <img src="/images/logo-white.png" alt="Season Expo" class="h-12 w-auto mx-auto mb-4">
                <p class="text-gray-400 max-w-2xl mx-auto">
                    <?php echo e($isArabic ? 'منصة المعارض الرائدة في الكويت - نربط بين الأحلام والفرص' : 'Kuwait\'s Leading Exhibition Platform - Connecting Dreams with Opportunities'); ?>

                </p>
            </div>

            <div class="border-t border-gray-800 pt-8">
                <p class="text-gray-400">
                    <?php echo e($isArabic ? '© 2024 جميع الحقوق محفوظة - منصة المعارض' : '© 2024 All Rights Reserved - Exhibition Platform'); ?>

                </p>
            </div>
        </div>
    </footer>

    <script>
        // Auto-hide success message after 8 seconds
        <?php if(session('success')): ?>
            setTimeout(function() {
                const successMessage = document.querySelector('[role="alert"]');
                if (successMessage) {
                    successMessage.style.opacity = '0';
                    successMessage.style.transition = 'opacity 0.5s ease-out';
                    setTimeout(function() {
                        successMessage.style.display = 'none';
                    }, 500);
                }
            }, 8000);
        <?php endif; ?>
    </script>
</body>
</html><?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/contact.blade.php ENDPATH**/ ?>