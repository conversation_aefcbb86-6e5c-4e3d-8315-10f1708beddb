<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>الأجنحة المتاحة - {{ $exhibition->title }}</title>

    <!-- Custom Favicon -->
    <link rel="icon" type="image/svg+xml" href="/season-expo-favicon.svg">
    <link rel="icon" type="image/x-icon" href="/season-expo-favicon.ico">
    <link rel="shortcut icon" href="/season-expo-favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Header -->
            <div class="mb-8">
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
                        <li class="inline-flex items-center">
                            <a href="/" class="text-gray-700 hover:text-blue-600">الرئيسية</a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <span class="mx-2 text-gray-400">/</span>
                                <a href="/exhibitions" class="text-gray-700 hover:text-blue-600">المعارض</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <span class="mx-2 text-gray-400">/</span>
                                <a href="/exhibitions/{{ $exhibition->slug }}" class="text-gray-700 hover:text-blue-600">{{ $exhibition->title }}</a>
                            </div>
                        </li>
                        <li aria-current="page">
                            <div class="flex items-center">
                                <span class="mx-2 text-gray-400">/</span>
                                <span class="text-gray-500">الأجنحة</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <h1 class="text-3xl font-bold text-gray-900 mb-2">الأجنحة المتاحة</h1>
                <p class="text-gray-600">{{ $exhibition->title }}</p>
                <div class="mt-4 flex flex-row-reverse space-x-reverse space-x-4 text-sm text-gray-500">
                    <span>📅 {{ $exhibition->start_date->format('d/m/Y') }} - {{ $exhibition->end_date->format('d/m/Y') }}</span>
                    <span>📍 {{ $exhibition->city }}, {{ $exhibition->country }}</span>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">تصفية الأجنحة</h3>
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="size" class="block text-sm font-medium text-gray-700 mb-2">الحجم</label>
                        <select name="size" id="size" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع الأحجام</option>
                            <option value="small" {{ request('size') == 'small' ? 'selected' : '' }}>صغير</option>
                            <option value="medium" {{ request('size') == 'medium' ? 'selected' : '' }}>متوسط</option>
                            <option value="large" {{ request('size') == 'large' ? 'selected' : '' }}>كبير</option>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="available" {{ request('status') == 'available' ? 'selected' : '' }}>متاح</option>
                            <option value="reserved" {{ request('status') == 'reserved' ? 'selected' : '' }}>محجوز</option>
                            <option value="booked" {{ request('status') == 'booked' ? 'selected' : '' }}>مؤكد</option>
                        </select>
                    </div>
                    <div class="md:col-span-2 flex items-end">
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors ml-2">
                            تطبيق التصفية
                        </button>
                        <a href="/exhibitions/{{ $exhibition->slug }}/booths" class="bg-gray-100 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-200 transition-colors">
                            إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>

            <!-- Booth Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-bold">📊</span>
                            </div>
                        </div>
                        <div class="mr-5">
                            <p class="text-sm font-medium text-gray-500">إجمالي الأجنحة</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $booths->total() }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 font-bold">✅</span>
                            </div>
                        </div>
                        <div class="mr-5">
                            <p class="text-sm font-medium text-gray-500">الأجنحة المتاحة</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $booths->where('status', 'available')->count() }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <span class="text-yellow-600 font-bold">⏳</span>
                            </div>
                        </div>
                        <div class="mr-5">
                            <p class="text-sm font-medium text-gray-500">محجوز مؤقتاً</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $booths->where('status', 'reserved')->count() }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <span class="text-red-600 font-bold">🔒</span>
                            </div>
                        </div>
                        <div class="mr-5">
                            <p class="text-sm font-medium text-gray-500">مؤكد الحجز</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $booths->where('status', 'booked')->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booths Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($booths as $booth)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">{{ $booth->booth_number }}</h3>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $booth->status === 'available' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $booth->status === 'reserved' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                    {{ $booth->status === 'booked' ? 'bg-red-100 text-red-800' : '' }}">
                                    {{ $booth->status === 'available' ? 'متاح' : '' }}
                                    {{ $booth->status === 'reserved' ? 'محجوز' : '' }}
                                    {{ $booth->status === 'booked' ? 'مؤكد' : '' }}
                                </span>
                            </div>

                            <div class="space-y-2 mb-4">
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">الحجم:</span>
                                    {{ $booth->size === 'small' ? 'صغير' : '' }}
                                    {{ $booth->size === 'medium' ? 'متوسط' : '' }}
                                    {{ $booth->size === 'large' ? 'كبير' : '' }}
                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">المساحة:</span> {{ $booth->area }} متر مربع
                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">الموقع:</span> {{ $booth->location }}
                                </p>
                                <p class="text-lg font-bold text-blue-600">
                                    {{ number_format($booth->price, 3) }} د.ك
                                </p>
                            </div>

                            @if($booth->features && is_array($booth->features))
                                <div class="mb-4">
                                    <p class="text-sm font-medium text-gray-700 mb-2">المميزات:</p>
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($booth->features as $feature)
                                            <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                                {{ $feature === 'electricity' ? 'كهرباء' : $feature }}
                                                {{ $feature === 'wifi' ? 'واي فاي' : '' }}
                                                {{ $feature === 'storage' ? 'تخزين' : '' }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="flex space-x-reverse space-x-2">
                                <a href="/exhibitions/{{ $exhibition->slug }}/booths/{{ $booth->id }}"
                                   class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                                    عرض التفاصيل
                                </a>
                                @if($booth->status === 'available')
                                    <!-- Simple form without JavaScript confirmation -->
                                    <form method="POST" action="/exhibitions/{{ $exhibition->slug }}/booths/{{ $booth->id }}/book" class="flex-1">
                                        @csrf
                                        <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                                                onclick="this.textContent='جاري الحجز...'; this.disabled=true; this.form.submit();">
                                            احجز الآن
                                        </button>
                                    </form>
                                @else
                                    <button disabled class="flex-1 bg-gray-400 text-gray-600 py-2 px-4 rounded-md cursor-not-allowed">
                                        غير متاح
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 text-6xl mb-4">🏢</div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أجنحة متاحة</h3>
                        <p class="text-gray-500">لم يتم العثور على أجنحة تطابق معايير البحث الخاصة بك.</p>
                        <a href="/exhibitions/{{ $exhibition->slug }}/booths" class="mt-4 inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            عرض جميع الأجنحة
                        </a>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($booths->hasPages())
                <div class="mt-8 flex justify-center">
                    {{ $booths->links() }}
                </div>
            @endif
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500">
                <p>© 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        function confirmBooking(form) {
            // Debug information
            console.log('Booking form submitted:', form);
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);

            // Check if user is logged in (basic check)
            const isLoggedIn = document.querySelector('nav a[href="/dashboard"]') !== null;

            if (!isLoggedIn) {
                alert('يرجى تسجيل الدخول أولاً لحجز الجناح');
                window.location.href = '/login-simple';
                return false;
            }

            // Confirm booking
            const confirmed = confirm('هل أنت متأكد من حجز هذا الجناح؟\nسيتم توجيهك لصفحة الدفع.');

            if (confirmed) {
                // Show loading state
                const button = form.querySelector('button[type="submit"]');
                const originalText = button.textContent;
                button.textContent = 'جاري الحجز...';
                button.disabled = true;

                console.log('Form confirmed, submitting...');

                // Actually submit the form
                try {
                    form.submit();
                } catch (error) {
                    console.error('Form submission error:', error);
                    alert('حدث خطأ في إرسال النموذج. يرجى المحاولة مرة أخرى.');
                    button.textContent = originalText;
                    button.disabled = false;
                }

                return true;
            }

            console.log('Booking cancelled by user');
            return false;
        }

        // Debug: Log all booking forms on page load
        document.addEventListener('DOMContentLoaded', function() {
            const bookingForms = document.querySelectorAll('form[action*="/book"]');
            console.log('Found booking forms:', bookingForms.length);

            bookingForms.forEach((form, index) => {
                console.log(`Form ${index + 1}:`, {
                    action: form.action,
                    method: form.method,
                    hasCSRF: form.querySelector('input[name="_token"]') !== null
                });
            });
        });
    </script>
</body>
</html>
