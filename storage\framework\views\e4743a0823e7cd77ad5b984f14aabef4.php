<?php
    $isArabic = ($locale ?? 'ar') === 'ar';
?>
<!DOCTYPE html>
<html lang="<?php echo e($isArabic ? 'ar' : 'en'); ?>" dir="<?php echo e($isArabic ? 'rtl' : 'ltr'); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo e($isArabic ? 'حسابي' : 'My Account'); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    <link rel="manifest" href="/images/site.webmanifest">

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Print Styles -->
    <link rel="stylesheet" href="/css/print-styles.css" media="print">

    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative group">
                        <button class="flex items-center text-gray-600 hover:text-gray-900 px-3 py-2 rounded-lg hover:bg-gray-100">
                            <span class="text-lg mr-2">🌐</span>
                            <span><?php echo e($isArabic ? 'العربية' : 'English'); ?></span>
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute <?php echo e($isArabic ? 'left-0' : 'right-0'); ?> mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <a href="/set-language/ar" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">
                                <span class="text-lg mr-3">🇰🇼</span>
                                <span>العربية</span>
                                <?php if($isArabic): ?><span class="mr-auto text-green-600">✓</span><?php endif; ?>
                            </a>
                            <a href="/set-language/en" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-b-lg">
                                <span class="text-lg mr-3">🇺🇸</span>
                                <span>English</span>
                                <?php if(!$isArabic): ?><span class="mr-auto text-green-600">✓</span><?php endif; ?>
                            </a>
                        </div>
                    </div>

                    <?php if(auth()->user()->role === 'admin'): ?>
                        <a href="/admin/exhibitions" class="text-red-600 hover:text-red-700 font-semibold"><?php echo e($isArabic ? 'إدارة المعارض' : 'Manage Exhibitions'); ?></a>
                    <?php endif; ?>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'المعارض' : 'Exhibitions'); ?></a>

                    <a href="/my-bookings" class="text-purple-600 hover:text-purple-700 font-semibold"><?php echo e($isArabic ? '📋 حجوزاتي' : '📋 My Bookings'); ?></a>

                    <a href="/signatures" class="text-emerald-600 hover:text-emerald-700 font-semibold"><?php echo e($isArabic ? 'التوقيعات الرقمية' : 'Digital Signatures'); ?></a>

                    <a href="/profile" class="text-blue-600 hover:text-blue-700 font-semibold"><?php echo e($isArabic ? 'الملف الشخصي' : 'Profile'); ?></a>
                    <a href="/" class="text-gray-600 hover:text-gray-900"><?php echo e($isArabic ? 'الصفحة الرئيسية' : 'Home'); ?></a>
                    <form method="POST" action="/logout" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-gray-600 hover:text-gray-900 bg-transparent border-none cursor-pointer">
                            <?php echo e($isArabic ? 'تسجيل الخروج' : 'Logout'); ?>

                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">



            <!-- Welcome Section -->
            <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg mb-8">
                <div class="p-6 text-gray-900">
                    <div class="flex items-center mb-4">
                        <img src="/images/logo.png" alt="Season Expo" class="h-8 w-auto ml-3">
                        <h2 class="text-2xl font-bold"><?php echo e($isArabic ? 'مرحباً بك في حسابك' : 'Welcome to Your Account'); ?></h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        <?php echo e($isArabic ? 'منصة حجز المعارض والأجنحة الخاصة بك جاهزة. ابدأ في استكشاف المعارض واحجز مساحة الجناح المثالية.' : 'Your exhibition and booth booking platform is ready. Start exploring exhibitions and book the perfect booth space.'); ?>

                    </p>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">🏢</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">المعارض النشطة</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e(\App\Models\Exhibition::where('status', 'active')->count()); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">📋</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">حجوزاتي</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e(auth()->user()->bookings()->count()); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">✍️</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">توقيعاتي الرقمية</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e(\App\Models\DigitalSignature::where('user_id', auth()->id())->count()); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">📊</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">النشاط الشهري</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e(auth()->user()->bookings()->whereMonth('created_at', now()->month)->count()); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4"><?php echo e($isArabic ? 'الإجراءات السريعة' : 'Quick Actions'); ?></h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                    <!-- Digital Signatures -->
                    <a href="/signatures" class="flex items-center p-4 border border-emerald-300 rounded-lg hover:border-emerald-400 hover:bg-emerald-50 transition-colors bg-emerald-25">
                        <div class="text-2xl mr-3">✍️</div>
                        <div>
                            <div class="font-medium text-gray-900"><?php echo e($isArabic ? 'التوقيعات الرقمية' : 'Digital Signatures'); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($isArabic ? 'إنشاء وإدارة التوقيعات والمستندات الرقمية' : 'Create and manage digital signatures and documents'); ?></div>
                        </div>
                    </a>

                    <?php if(auth()->user()->role === 'admin'): ?>
                        <!-- Admin Users Management -->
                        <a href="/admin/users" class="flex items-center p-4 border border-indigo-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors">
                            <div class="text-2xl mr-3">👥</div>
                            <div>
                                <div class="font-medium text-gray-900">إدارة المستخدمين</div>
                                <div class="text-sm text-gray-500">عرض وإدارة جميع مستخدمي المنصة</div>
                            </div>
                        </a>
                    <?php endif; ?>

                    <a href="/my-bookings" class="flex items-center p-4 border border-purple-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors">
                        <svg class="h-6 w-6 text-purple-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">📋 حجوزاتي</div>
                            <div class="text-sm text-gray-500">عرض وإدارة حجوزاتك</div>
                        </div>
                    </a>

                    <a href="/exhibitions" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                        <svg class="h-6 w-6 text-blue-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">تصفح المعارض</div>
                            <div class="text-sm text-gray-500">اعثر على الحدث التالي</div>
                        </div>
                    </a>

                    <a href="/dashboard/booth-search" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors">
                        <svg class="h-6 w-6 text-green-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">البحث عن الأجنحة</div>
                            <div class="text-sm text-gray-500">ابحث عن المساحة المثالية</div>
                        </div>
                    </a>

                    <a href="/dashboard/reservations" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors">
                        <svg class="h-6 w-6 text-purple-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">إدارة الحجوزات</div>
                            <div class="text-sm text-gray-500">إدارة جميع حجوزات المنصة</div>
                        </div>
                    </a>

                    <a href="/profile" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors">
                        <svg class="h-6 w-6 text-indigo-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">الملف الشخصي</div>
                            <div class="text-sm text-gray-500">إدارة معلوماتك</div>
                        </div>
                    </a>

                    <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors">
                        <svg class="h-6 w-6 text-orange-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">الحصول على الدعم</div>
                            <div class="text-sm text-gray-500">تحتاج مساعدة؟</div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-12 text-center text-gray-500">
                <p>&copy; <?php echo e(date('Y')); ?> جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <!-- Print Helper Script -->
    <script src="/js/print-helper.js"></script>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/dashboard-simple.blade.php ENDPATH**/ ?>