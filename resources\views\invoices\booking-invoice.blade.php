<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>فاتورة الحجز #{{ $booking->id }} - Season Expo Kuwait</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 15mm;
            background: white;
            color: #333;
            line-height: 1.5;
            font-size: 14px;
        }

        .invoice-container {
            max-width: 210mm;
            width: 100%;
            margin: 0 auto;
            background: white;
            min-height: 297mm;
            box-sizing: border-box;
        }

        .invoice-header {
            background: white;
            padding: 20mm 0 10mm 0;
            text-align: center;
            border-bottom: 2px solid #3b82f6;
            margin-bottom: 15mm;
        }

        .invoice-header .logo {
            max-height: 60px;
            max-width: 200px;
            margin: 0 auto 10px auto;
            display: block;
        }

        .invoice-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: bold;
            color: #1e40af;
        }

        .invoice-header p {
            margin: 5px 0 0 0;
            font-size: 1rem;
            color: #666;
        }

        .invoice-body {
            padding: 0;
        }

        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15mm;
            flex-wrap: wrap;
            gap: 10mm;
        }

        .invoice-info div {
            flex: 1;
            min-width: 80mm;
            margin-bottom: 10mm;
        }

        .invoice-info h3 {
            color: #1e40af;
            margin-bottom: 8px;
            font-size: 1.1rem;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 3px;
        }

        .invoice-info p {
            margin: 3px 0;
            color: #666;
            font-size: 13px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10mm 0;
            font-size: 13px;
        }

        .invoice-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: bold;
            padding: 8mm;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-table td {
            padding: 6mm;
            border-bottom: 1px solid #e5e7eb;
        }

        .total-section {
            background: #f8fafc;
            padding: 10mm;
            border-radius: 4px;
            margin-top: 10mm;
            width: 60mm;
            margin-left: auto;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 5mm 0;
            font-size: 14px;
        }

        .total-row.final {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            border-top: 2px solid #3b82f6;
            padding-top: 8mm;
            margin-top: 8mm;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-confirmed {
            background: #d1fae5;
            color: #065f46;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-cancelled {
            background: #fee2e2;
            color: #991b1b;
        }

        .footer {
            background: #f8fafc;
            padding: 10mm;
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 15mm;
            border-top: 1px solid #e5e7eb;
        }

        .print-date {
            text-align: center;
            color: #666;
            font-size: 11px;
            margin-top: 10mm;
        }

        @media print {
            @page {
                size: A4;
                margin: 15mm;
            }

            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }

            .invoice-container {
                max-width: none;
                width: 100%;
                min-height: auto;
                page-break-inside: avoid;
            }

            .invoice-header {
                padding: 10mm 0 5mm 0;
                margin-bottom: 10mm;
            }

            .invoice-info {
                margin-bottom: 10mm;
            }

            .total-section {
                margin-top: 8mm;
                padding: 8mm;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <img src="/images/logo.png" alt="Season Expo Kuwait" class="logo" onerror="this.style.display='none'">
            <h1>فاتورة حجز الجناح</h1>
            <p>Season Expo Kuwait</p>
        </div>

        <!-- Body -->
        <div class="invoice-body">
            <!-- Invoice Info -->
            <div class="invoice-info">
                <div>
                    <h3>📋 معلومات الفاتورة</h3>
                    <p><strong>رقم الفاتورة:</strong> INV-{{ $booking->id }}</p>
                    <p><strong>رقم الحجز:</strong> #{{ $booking->id }}</p>
                    <p><strong>تاريخ الإصدار:</strong> {{ now()->format('d/m/Y') }}</p>
                    <p><strong>تاريخ الحجز:</strong> {{ $booking->created_at->format('d/m/Y') }}</p>
                    <p><strong>الحالة:</strong>
                        @if($booking->status == 'confirmed')
                            <span class="status-badge status-confirmed">مؤكد</span>
                        @elseif($booking->status == 'pending')
                            <span class="status-badge status-pending">قيد الانتظار</span>
                        @else
                            <span class="status-badge status-cancelled">ملغي</span>
                        @endif
                    </p>
                </div>

                <div>
                    <h3>👤 معلومات العميل</h3>
                    <p><strong>الاسم:</strong> {{ $booking->user->name }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ $booking->user->email }}</p>
                    @if($booking->user->phone)
                        <p><strong>الهاتف:</strong> {{ $booking->user->phone }}</p>
                    @endif
                    @if($booking->user->company)
                        <p><strong>الشركة:</strong> {{ $booking->user->company }}</p>
                    @endif
                </div>
            </div>

            <!-- Booking Details Table -->
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>التفاصيل</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>المعرض</strong></td>
                        <td>{{ $booking->exhibition->title ?? 'غير محدد' }}</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td><strong>رقم الجناح</strong></td>
                        <td>{{ $booking->booth->booth_number ?? 'غير محدد' }}</td>
                        <td>-</td>
                    </tr>
                    @if($booking->booth && $booking->booth->area)
                        <tr>
                            <td><strong>مساحة الجناح</strong></td>
                            <td>{{ $booking->booth->area }} متر مربع</td>
                            <td>-</td>
                        </tr>
                    @endif
                    <tr>
                        <td><strong>رسوم الحجز</strong></td>
                        <td>حجز جناح في {{ $booking->exhibition->title ?? 'المعرض' }}</td>
                        <td>{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</td>
                    </tr>
                </tbody>
            </table>

            <!-- Total Section -->
            <div class="total-section">
                <div class="total-row final">
                    <span>المبلغ الإجمالي:</span>
                    <span>{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</span>
                </div>
            </div>

            <!-- Payment Info -->
            @if($booking->payments && $booking->payments->count() > 0)
                <div style="margin-top: 10mm;">
                    <h3 style="color: #1e40af; margin-bottom: 8mm; font-size: 1.1rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 3px;">💳 معلومات الدفع</h3>
                    @foreach($booking->payments as $payment)
                        <div style="background: #f9fafb; padding: 8mm; border-radius: 4px; margin-bottom: 5mm; font-size: 13px;">
                            <p style="margin: 3px 0;"><strong>طريقة الدفع:</strong> {{ $payment->payment_method ?? 'غير محدد' }}</p>
                            <p style="margin: 3px 0;"><strong>حالة الدفع:</strong>
                                @if($payment->status == 'completed')
                                    <span class="status-badge status-confirmed">مكتمل</span>
                                @elseif($payment->status == 'pending')
                                    <span class="status-badge status-pending">قيد المعالجة</span>
                                @else
                                    <span class="status-badge status-cancelled">فاشل</span>
                                @endif
                            </p>
                            @if($payment->paid_at)
                                <p style="margin: 3px 0;"><strong>تاريخ الدفع:</strong> {{ $payment->paid_at->format('d/m/Y H:i') }}</p>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Season Expo Kuwait</strong></p>
            <p>شكراً لاختياركم معارضنا. نتطلع لرؤيتكم في المعرض!</p>
            <p>للاستفسارات: <EMAIL> | +965 1234 5678</p>
        </div>
    </div>

    <div class="print-date">
        تم إنشاء هذه الفاتورة في: {{ now()->format('d/m/Y H:i:s') }}
    </div>

    <script>
        // Auto-print when opened as download
        window.onload = function() {
            if (window.location.search.includes('print=1')) {
                window.print();
            }
        };
    </script>
</body>
</html>
