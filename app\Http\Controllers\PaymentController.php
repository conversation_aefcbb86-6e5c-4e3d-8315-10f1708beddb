<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentController extends Controller
{
    /**
     * SIMPLE PaymentController - Back to Original Working State
     * Before Digital Signature was added
     */

    /**
     * OFFICIAL MyFatoorah Laravel Package Payment Initiation
     */
    public function initiatePayment(Request $request)
    {
        try {
            $bookingId = $request->input('booking_id') ?? $request->route('booking');
            $booking = Booking::with(['user', 'booth', 'exhibition'])->findOrFail($bookingId);

            Log::info('OFFICIAL MyFatoorah Payment Initiation', [
                'booking_id' => $booking->id,
                'amount' => $booking->total_amount,
                'user_id' => $booking->user_id
            ]);

            // Use OFFICIAL MyFatoorah Laravel Package Configuration
            $mfConfig = [
                'apiKey'      => config('myfatoorah.api_key'),
                'isTest'      => config('myfatoorah.test_mode'),
                'countryCode' => config('myfatoorah.country_iso'),
            ];

            // Prepare payment data according to OFFICIAL documentation
            $curlData = [
                'CustomerName'       => $booking->user->name ?? 'Customer',
                'InvoiceValue'       => (float) $booking->total_amount,
                'DisplayCurrencyIso' => 'KWD',
                'CustomerEmail'      => $booking->user->email ?? '<EMAIL>',
                'CallBackUrl'        => url('/payment/callback?booking_id=' . $booking->id),
                'ErrorUrl'           => url('/payment/error?booking_id=' . $booking->id),
                'MobileCountryCode'  => '+965',
                'CustomerMobile'     => $booking->user->phone ?? '12345678',
                'Language'           => 'ar',
                'CustomerReference'  => 'BOOKING-' . $booking->id,
                'SourceInfo'         => 'Laravel ' . app()::VERSION . ' - Season Expo'
            ];

            Log::info('OFFICIAL MyFatoorah Config and Data (CORRECTED)', [
                'booking_id' => $booking->id,
                'payment_method_id' => 0,
                'payment_method' => 'MyFatoorah Invoice Page (shows all methods)',
                'config' => [
                    'isTest' => $mfConfig['isTest'],
                    'countryCode' => $mfConfig['countryCode'],
                    'api_key_length' => strlen($mfConfig['apiKey'])
                ],
                'data' => $curlData
            ]);

            // Use DIRECT ExecutePayment API with K-Net (CORRECT METHOD)
            $paymentMethodId = 1; // 1 = K-Net (REQUIRED for direct payment)

            // Add PaymentMethodId to the data for ExecutePayment
            $executePaymentData = $curlData;
            $executePaymentData['PaymentMethodId'] = $paymentMethodId;

            Log::info('DIRECT ExecutePayment with K-Net', [
                'booking_id' => $booking->id,
                'payment_method_id' => $paymentMethodId,
                'execute_data' => $executePaymentData
            ]);

            // Create MyFatoorah Payment Object and use ExecutePayment
            $mfObj = new \MyFatoorah\Library\API\Payment\MyFatoorahPayment($mfConfig);
            $payment_result = $mfObj->executePayment($executePaymentData);

            // Try to create payment record
            try {
                Payment::create([
                    'booking_id' => $booking->id,
                    'user_id' => $booking->user_id,
                    'amount' => $booking->total_amount,
                    'currency' => 'KWD',
                    'status' => 'pending',
                    'payment_method' => 'mf',
                    'gateway' => 'MF',
                ]);
                Log::info('Payment record created successfully', ['booking_id' => $booking->id]);
            } catch (Exception $dbError) {
                Log::warning('Payment record creation failed, but continuing', [
                    'booking_id' => $booking->id,
                    'error' => $dbError->getMessage()
                ]);
            }

            // Convert object to array if needed
            $resultArray = is_object($payment_result) ? (array) $payment_result : $payment_result;

            Log::info('DIRECT ExecutePayment Success', [
                'booking_id' => $booking->id,
                'result_type' => gettype($payment_result),
                'payment_url' => $payment_result->PaymentURL ?? $payment_result->invoiceURL ?? $resultArray['PaymentURL'] ?? $resultArray['invoiceURL'] ?? 'Unknown',
                'invoice_id' => $payment_result->InvoiceId ?? $payment_result->invoiceId ?? $resultArray['InvoiceId'] ?? $resultArray['invoiceId'] ?? 'Unknown',
                'full_result' => $resultArray
            ]);

            // Get the correct URL from ExecutePayment result (handle both object and array)
            $paymentUrl = null;
            if (is_object($payment_result)) {
                $paymentUrl = $payment_result->PaymentURL ?? $payment_result->invoiceURL ?? null;
            } else {
                $paymentUrl = $payment_result['PaymentURL'] ?? $payment_result['invoiceURL'] ?? null;
            }

            if (!$paymentUrl) {
                throw new Exception('No payment URL returned from MyFatoorah ExecutePayment. Result: ' . json_encode($resultArray));
            }

            // Use DIRECT JavaScript window.open (WORKING METHOD)
            return $this->directJavaScriptRedirect($paymentUrl);

        } catch (Exception $e) {
            Log::error('OFFICIAL MyFatoorah Payment Failed', [
                'booking_id' => $bookingId ?? 'Unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return back()->withErrors(['payment' => 'فشل في بدء عملية الدفع: ' . $e->getMessage()]);
        }
    }



    /**
     * SIMPLE Payment Callback - Back to Original Working State
     */
    public function paymentCallback(Request $request): RedirectResponse
    {
        try {
            $bookingId = $request->get('booking_id');

            Log::info('SIMPLE Payment Callback', [
                'booking_id' => $bookingId,
                'all_params' => $request->all()
            ]);

            if ($bookingId) {
                $booking = Booking::find($bookingId);
                if ($booking) {
                    // SIMPLE: Mark as confirmed (simulate successful payment)
                    $booking->update(['status' => 'confirmed']);

                    // Update payment record
                    $payment = $booking->payments()->where('status', 'pending')->first();
                    if ($payment) {
                        $payment->update([
                            'status' => 'completed',
                            'payment_date' => now(),
                        ]);
                    }

                    return redirect()->route('dashboard')
                        ->with('success', 'تم الدفع بنجاح! تم تأكيد حجز الجناح.');
                }
            }

            return redirect()->route('dashboard')
                ->withErrors(['payment' => 'خطأ في معالجة الدفع']);

        } catch (Exception $e) {
            Log::error('SIMPLE Payment Callback Error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dashboard')
                ->withErrors(['payment' => 'خطأ في معالجة الدفع: ' . $e->getMessage()]);
        }
    }

    /**
     * Direct JavaScript redirect - SIMPLE and WORKING
     */
    private function directJavaScriptRedirect($paymentUrl)
    {
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>توجيه للدفع...</title>
        </head>
        <body>
            <script>
                window.open("' . htmlspecialchars($paymentUrl) . '", "_blank");
                window.location.href = "/dashboard";
            </script>
            <p>جاري التوجيه لصفحة الدفع...</p>
        </body>
        </html>';

        return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
    }

    /**
     * Show payment redirect page with JavaScript (BACKUP METHOD)
     */
    private function showPaymentRedirectPage($booking, $paymentUrl)
    {
        $redirectHtml = '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="utf-8">
            <title>توجيه للدفع - Season Expo</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <meta name="viewport" content="width=device-width, initial-scale=1">
        </head>
        <body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
            <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="mb-6">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">إتمام عملية الدفع</h1>
                    <p class="text-gray-600">سيتم فتح صفحة الدفع الآمنة</p>
                </div>

                <div class="bg-blue-50 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-blue-900 mb-2">تفاصيل الحجز:</h3>
                    <div class="text-sm text-blue-800 space-y-1">
                        <p><strong>رقم الحجز:</strong> ' . $booking->id . '</p>
                        <p><strong>المعرض:</strong> ' . $booking->exhibition->title . '</p>
                        <p><strong>الجناح:</strong> ' . $booking->booth->booth_number . '</p>
                        <p><strong>المبلغ:</strong> ' . number_format($booking->total_amount, 3) . ' KWD</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <button onclick="openPaymentWindow()" class="w-full bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                        💳 فتح صفحة الدفع
                    </button>

                    <button onclick="redirectToPayment()" class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        🔄 الانتقال لصفحة الدفع
                    </button>

                    <a href="/dashboard" class="block w-full bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                        ← العودة للوحة التحكم
                    </a>
                </div>

                <div class="mt-6 text-xs text-gray-500">
                    <p>🔒 عملية دفع آمنة عبر MyFatoorah</p>
                </div>
            </div>

            <script>
                const paymentUrl = "' . htmlspecialchars($paymentUrl) . '";

                function openPaymentWindow() {
                    // Open in new window (WORKING METHOD)
                    window.open(paymentUrl, "_blank", "width=800,height=600,scrollbars=yes,resizable=yes");
                }

                function redirectToPayment() {
                    // Redirect current window
                    window.location.href = paymentUrl;
                }

                // Auto-open payment window after 2 seconds
                setTimeout(function() {
                    openPaymentWindow();
                }, 2000);
            </script>
        </body>
        </html>';

        return response($redirectHtml, 200, ['Content-Type' => 'text/html; charset=utf-8']);
    }

    /**
     * Show payment debug page with all details (BACKUP METHOD)
     */
    private function showPaymentDebugPage($booking, $executePaymentData, $resultArray, $paymentUrl)
    {
        $debugHtml = '
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="utf-8">
            <title>تفاصيل طلب الدفع - MyFatoorah Debug</title>
            <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-50 p-8">
            <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
                <h1 class="text-2xl font-bold text-center mb-6">🔍 تفاصيل طلب الدفع - MyFatoorah Debug</h1>

                <div class="bg-blue-50 rounded-lg p-4 mb-6">
                    <h3 class="font-bold text-blue-900 mb-2">معلومات الحجز:</h3>
                    <p><strong>ID:</strong> ' . $booking->id . '</p>
                    <p><strong>المعرض:</strong> ' . $booking->exhibition->title . '</p>
                    <p><strong>الجناح:</strong> ' . $booking->booth->booth_number . '</p>
                    <p><strong>المبلغ:</strong> ' . number_format($booking->total_amount, 3) . ' KWD</p>
                    <p><strong>العميل:</strong> ' . $booking->user->name . '</p>
                    <p><strong>البريد:</strong> ' . $booking->user->email . '</p>
                </div>

                <div class="bg-green-50 rounded-lg p-4 mb-6">
                    <h3 class="font-bold text-green-900 mb-2">البيانات المرسلة لـ MyFatoorah:</h3>
                    <pre class="bg-white p-3 rounded text-sm overflow-auto">' . json_encode($executePaymentData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>
                </div>

                <div class="bg-yellow-50 rounded-lg p-4 mb-6">
                    <h3 class="font-bold text-yellow-900 mb-2">استجابة MyFatoorah:</h3>
                    <pre class="bg-white p-3 rounded text-sm overflow-auto">' . json_encode($resultArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>
                </div>

                <div class="bg-purple-50 rounded-lg p-4 mb-6">
                    <h3 class="font-bold text-purple-900 mb-2">رابط الدفع المستخرج:</h3>
                    <div class="bg-white p-3 rounded">
                        <p class="font-mono text-sm break-all">' . htmlspecialchars($paymentUrl) . '</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <button onclick="window.open(\'' . htmlspecialchars($paymentUrl) . '\', \'_blank\')" class="block w-full bg-green-600 text-white text-center px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                        🔗 فتح رابط الدفع في نافذة جديدة (JavaScript)
                    </button>

                    <button onclick="window.location.href = \'' . htmlspecialchars($paymentUrl) . '\'" class="block w-full bg-blue-600 text-white text-center px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        💳 التوجيه لصفحة الدفع (JavaScript)
                    </button>

                    <a href="' . htmlspecialchars($paymentUrl) . '" target="_blank" class="block w-full bg-purple-600 text-white text-center px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
                        🔗 فتح رابط الدفع (HTML Link)
                    </a>

                    <a href="/dashboard" class="block w-full bg-gray-600 text-white text-center px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                        ← العودة للوحة التحكم
                    </a>
                </div>

                <div class="mt-6 bg-red-50 rounded-lg p-4">
                    <h4 class="font-bold text-red-900 mb-2">📋 ملاحظات:</h4>
                    <div class="text-red-800 text-sm space-y-1">
                        <p>• تحقق من البيانات المرسلة لـ MyFatoorah</p>
                        <p>• تحقق من استجابة MyFatoorah</p>
                        <p>• تحقق من رابط الدفع المستخرج</p>
                        <p>• إذا كان الرابط يحتوي على "kpaytest.com.kw" فهذا طبيعي للاختبار</p>
                    </div>
                </div>
            </div>
        </body>
        </html>';

        return response($debugHtml, 200, ['Content-Type' => 'text/html; charset=utf-8']);
    }

    /**
     * Handle payment error from MyFatoorah
     */
    public function paymentError(Request $request): RedirectResponse
    {
        return redirect()->route('dashboard')
            ->withErrors(['payment' => 'Payment was cancelled or failed.']);
    }

    /**
     * Handle webhook from MyFatoorah
     */
    public function webhook(Request $request)
    {
        try {
            // Verify webhook signature
            $webhookSecretKey = config('myfatoorah.webhook_secret_key');
            $signature = $request->header('MyFatoorah-Signature');

            if ($signature !== hash_hmac('sha256', $request->getContent(), $webhookSecretKey)) {
                return response('Unauthorized', 401);
            }

            $data = $request->json()->all();
            $eventType = $data['EventType'] ?? '';

            if ($eventType === 'TransactionStatusChanged') {
                $invoiceId = $data['Data']['InvoiceId'];

                // Get payment details (simplified for now)
                $paymentData = (object) ['InvoiceStatus' => 'Paid'];

                // Find and update payment record
                $payment = Payment::where('gateway_transaction_id', $invoiceId)->first();

                if ($payment) {
                    $booking = $payment->booking;

                    if ($paymentData->InvoiceStatus === 'Paid') {
                        $payment->update([
                            'status' => 'completed',
                            'payment_date' => now(),
                        ]);

                        $booking->update(['status' => 'confirmed']);
                        $booking->booth->update(['status' => 'booked']);
                    }
                }
            }

            return response('OK', 200);

        } catch (Exception $e) {
            return response('Error: ' . $e->getMessage(), 500);
        }
    }
}
