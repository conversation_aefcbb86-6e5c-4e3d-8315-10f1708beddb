<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تخطيط معرض {{ $exhibition->title ?? 'المعرض' }} - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .booth-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
            max-width: 1200px;
        }
        .booth-item {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        .booth-available {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: 2px solid #047857;
        }
        .booth-booked {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: 2px solid #b91c1c;
        }
        .booth-maintenance {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: 2px solid #b45309;
        }
        .booth-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .entrance {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: 2px solid #1d4ed8;
        }
        .stage {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: 2px solid #6d28d9;
        }
        .food-court {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            border: 2px solid #c2410c;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                    <span class="mr-4 px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded">Admin</span>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/admin/exhibitions" class="text-gray-600 hover:text-gray-900">إدارة المعارض</a>
                    <a href="/admin/exhibitions/{{ $exhibition->id ?? 1 }}/booths" class="text-blue-600 hover:text-blue-700">إدارة الأجنحة</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">تخطيط المعرض</h1>
                        <p class="text-gray-600 mt-2">{{ $exhibition->title ?? 'معرض التكنولوجيا والابتكار 2024' }}</p>
                    </div>
                    <a href="/admin/exhibitions/{{ $exhibition->id ?? 1 }}/booths" class="text-blue-600 hover:text-blue-700">← العودة لإدارة الأجنحة</a>
                </div>
            </div>

            <!-- Exhibition Info -->
            <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <div class="text-sm text-blue-600 font-medium">الموقع</div>
                        <div class="text-blue-900">{{ $exhibition->location ?? 'مركز الكويت الدولي للمعارض' }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">إجمالي الأجنحة</div>
                        <div class="text-blue-900">{{ count($exhibition->booths ?? []) ?: 20 }} جناح</div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">الأجنحة المتاحة</div>
                        <div class="text-blue-900">{{ collect($exhibition->booths ?? [])->where('status', 'available')->count() ?: 15 }} جناح</div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">الأجنحة المحجوزة</div>
                        <div class="text-blue-900">{{ collect($exhibition->booths ?? [])->where('status', 'booked')->count() ?: 5 }} جناح</div>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">مفتاح الألوان</h2>
                <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
                    <div class="flex items-center">
                        <div class="w-6 h-6 booth-available rounded ml-2"></div>
                        <span class="text-sm">متاح</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 booth-booked rounded ml-2"></div>
                        <span class="text-sm">محجوز</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 booth-maintenance rounded ml-2"></div>
                        <span class="text-sm">صيانة</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 entrance rounded ml-2"></div>
                        <span class="text-sm">مدخل</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 stage rounded ml-2"></div>
                        <span class="text-sm">منصة</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-6 h-6 food-court rounded ml-2"></div>
                        <span class="text-sm">مطاعم</span>
                    </div>
                </div>
            </div>

            <!-- Exhibition Layout -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">تخطيط قاعة المعرض</h2>

                <!-- Main Exhibition Hall -->
                <div class="bg-gray-100 rounded-lg p-8 relative">
                    <div class="text-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-700">القاعة الرئيسية</h3>
                    </div>

                    <!-- Top Entrances -->
                    <div class="flex justify-center mb-6">
                        <div class="booth-item entrance w-20 h-12">
                            مدخل رئيسي
                        </div>
                        <div class="w-8"></div>
                        <div class="booth-item entrance w-20 h-12">
                            مدخل فرعي
                        </div>
                    </div>

                    <!-- Booth Grid -->
                    <div class="booth-grid mb-6">
                        @if(isset($exhibition->booths) && count($exhibition->booths) > 0)
                            @foreach($exhibition->booths as $booth)
                                <div class="booth-item
                                    {{ $booth->status === 'available' ? 'booth-available' :
                                       ($booth->status === 'booked' ? 'booth-booked' : 'booth-maintenance') }}"
                                     onclick="showBoothDetails('{{ $booth->booth_number }}', '{{ $booth->status }}', '{{ $booth->size }}', '{{ $booth->price }}')">
                                    {{ $booth->booth_number }}
                                </div>
                            @endforeach
                        @else
                            <!-- Sample Layout for Demo -->
                            @for($i = 1; $i <= 20; $i++)
                                <div class="booth-item {{ $i <= 5 ? 'booth-booked' : 'booth-available' }}"
                                     onclick="showBoothDetails('A-{{ $i }}', '{{ $i <= 5 ? 'booked' : 'available' }}', '25', '500')">
                                    A-{{ $i }}
                                </div>
                            @endfor
                        @endif
                    </div>

                    <!-- Central Features -->
                    <div class="flex justify-center items-center gap-8 mb-6">
                        <div class="booth-item stage w-32 h-16">
                            منصة العروض
                        </div>
                        <div class="booth-item food-court w-32 h-16">
                            منطقة المطاعم
                        </div>
                    </div>

                    <!-- Bottom Facilities -->
                    <div class="flex justify-center gap-4">
                        <div class="booth-item entrance w-20 h-12">
                            مخرج طوارئ
                        </div>
                        <div class="booth-item entrance w-20 h-12">
                            دورات مياه
                        </div>
                        <div class="booth-item entrance w-20 h-12">
                            مكتب الإدارة
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">🎪</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-gray-900">{{ count($exhibition->booths ?? []) ?: 20 }}</div>
                            <div class="text-sm text-gray-500">إجمالي الأجنحة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">✅</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-green-600">{{ collect($exhibition->booths ?? [])->where('status', 'available')->count() ?: 15 }}</div>
                            <div class="text-sm text-gray-500">أجنحة متاحة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📋</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-red-600">{{ collect($exhibition->booths ?? [])->where('status', 'booked')->count() ?: 5 }}</div>
                            <div class="text-sm text-gray-500">أجنحة محجوزة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-blue-600">{{ round((collect($exhibition->booths ?? [])->where('status', 'booked')->count() / max(count($exhibition->booths ?? []), 1)) * 100) ?: 25 }}%</div>
                            <div class="text-sm text-gray-500">نسبة الإشغال</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booth Details Modal -->
    <div id="boothModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold" id="modalTitle">تفاصيل الجناح</h3>
                <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700">✕</button>
            </div>
            <div id="modalContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="mt-6 flex gap-3">
                <button onclick="closeModal()" class="flex-1 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    إغلاق
                </button>
                <a id="editBoothLink" href="#" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-center">
                    تعديل الجناح
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function showBoothDetails(boothNumber, status, size, price) {
            const modal = document.getElementById('boothModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            const editLink = document.getElementById('editBoothLink');

            title.textContent = `جناح ${boothNumber}`;

            const statusText = status === 'available' ? 'متاح' :
                              status === 'booked' ? 'محجوز' : 'صيانة';
            const statusColor = status === 'available' ? 'text-green-600' :
                               status === 'booked' ? 'text-red-600' : 'text-yellow-600';

            content.innerHTML = `
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="font-medium">رقم الجناح:</span>
                        <span>${boothNumber}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium">الحالة:</span>
                        <span class="${statusColor} font-semibold">${statusText}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium">المساحة:</span>
                        <span>${size} متر مربع</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium">السعر:</span>
                        <span>${price} د.ك</span>
                    </div>
                </div>
            `;

            editLink.href = `/admin/booths/${boothNumber}/edit`;
            modal.classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('boothModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('boothModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
