<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('البحث عن الأجنحة') }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        body {
            font-family: {{ app()->getLocale() == 'ar' ? "'Noto Sans Arabic', sans-serif" : "'Figtree', sans-serif" }};
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="/" class="flex-shrink-0">
                            <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                            <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            {{ __('حسابي') }}
                        </a>
                        <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            {{ __('حجوزاتي') }}
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Error Message -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
                    <div class="text-center">
                        <!-- Error Icon -->
                        <div class="text-red-400 text-6xl mb-4">⚠️</div>
                        
                        <!-- Error Title -->
                        <h1 class="text-2xl font-bold text-gray-900 mb-4">{{ __('مشكلة في تحميل البيانات') }}</h1>
                        
                        <!-- Error Description -->
                        <p class="text-gray-600 mb-6">
                            {{ __('عذراً، حدثت مشكلة أثناء تحميل بيانات الأجنحة. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.') }}
                        </p>
                        
                        <!-- Technical Error (for debugging) -->
                        @if(isset($error) && config('app.debug'))
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
                                <h3 class="font-semibold text-red-800 mb-2">Technical Error:</h3>
                                <code class="text-sm text-red-700">{{ $error }}</code>
                            </div>
                        @endif
                        
                        <!-- Action Buttons -->
                        <div class="flex justify-center space-x-4 rtl:space-x-reverse">
                            <button onclick="window.location.reload()" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
                                🔄 {{ __('إعادة المحاولة') }}
                            </button>
                            
                            <a href="/dashboard"
                               class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium">
                                🏠 {{ __('العودة لحسابي') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Alternative Options -->
                <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ __('خيارات بديلة') }}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- View Exhibitions -->
                        <a href="/exhibitions" 
                           class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                            <div class="text-blue-600 text-2xl ml-3">🏢</div>
                            <div>
                                <div class="font-medium text-gray-900">{{ __('تصفح المعارض') }}</div>
                                <div class="text-sm text-gray-500">{{ __('عرض جميع المعارض المتاحة') }}</div>
                            </div>
                        </a>

                        <!-- Contact Support -->
                        <a href="/contact" 
                           class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors">
                            <div class="text-green-600 text-2xl ml-3">📞</div>
                            <div>
                                <div class="font-medium text-gray-900">{{ __('اتصل بنا') }}</div>
                                <div class="text-sm text-gray-500">{{ __('تواصل مع فريق الدعم') }}</div>
                            </div>
                        </a>

                        <!-- My Reservations -->
                        <a href="/dashboard/reservations" 
                           class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors">
                            <div class="text-purple-600 text-2xl ml-3">📋</div>
                            <div>
                                <div class="font-medium text-gray-900">{{ __('حجوزاتي') }}</div>
                                <div class="text-sm text-gray-500">{{ __('عرض الحجوزات الحالية') }}</div>
                            </div>
                        </a>

                        <!-- Print Page -->
                        <button onclick="window.print()" 
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors">
                            <div class="text-gray-600 text-2xl ml-3">🖨️</div>
                            <div>
                                <div class="font-medium text-gray-900">{{ __('طباعة الصفحة') }}</div>
                                <div class="text-sm text-gray-500">{{ __('طباعة تفاصيل المشكلة') }}</div>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Help Information -->
                <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="font-semibold text-blue-900 mb-3">💡 {{ __('نصائح للمساعدة') }}</h3>
                    <ul class="text-blue-800 space-y-2">
                        <li>• {{ __('تأكد من اتصالك بالإنترنت') }}</li>
                        <li>• {{ __('جرب تحديث الصفحة (F5)') }}</li>
                        <li>• {{ __('امسح ذاكرة التخزين المؤقت للمتصفح') }}</li>
                        <li>• {{ __('جرب متصفح آخر') }}</li>
                        <li>• {{ __('إذا استمرت المشكلة، اتصل بالدعم الفني') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Styles -->
    <style media="print">
        nav, .no-print { display: none !important; }
        body { background: white !important; }
        .shadow-sm, .shadow-md { box-shadow: none !important; }
        .border { border: 1px solid #ccc !important; }
    </style>
</body>
</html>
