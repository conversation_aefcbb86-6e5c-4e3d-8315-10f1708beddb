/**
 * Logo Configuration for Season Expo Platform
 * Centralized configuration for all logo-related settings
 */

window.LogoConfig = {
    // Logo paths
    paths: {
        default: '/images/logo.png',
        white: '/images/logo-white.png',
        small: '/images/logo-small.png',
        favicon: '/images/favicon.png',
        appleTouchIcon: '/images/apple-touch-icon.png',
        androidChrome192: '/images/android-chrome-192x192.png',
        androidChrome512: '/images/android-chrome-512x512.png'
    },

    // Platform information
    platform: {
        name: 'منصة المعارض',
        nameEn: 'Season Expo',
        tagline: 'منصة المعارض الموسمية الرائدة',
        taglineEn: 'Leading Seasonal Exhibition Platform',
        url: window.location.origin,
        copyright: 'جميع الحقوق محفوظة',
        copyrightEn: 'All Rights Reserved'
    },

    // Size configurations
    sizes: {
        xs: 'h-6',      // 24px
        sm: 'h-8',      // 32px
        md: 'h-10',     // 40px (default)
        lg: 'h-12',     // 48px
        xl: 'h-16',     // 64px
        '2xl': 'h-20',  // 80px
        '3xl': 'h-24'   // 96px
    },

    // Print settings
    print: {
        header: {
            enabled: true,
            logoHeight: '60px',
            showTitle: true,
            showUrl: true,
            showDate: true
        },
        footer: {
            enabled: true,
            logoHeight: '30px',
            showCopyright: true,
            showUrl: true
        },
        watermark: {
            enabled: false,
            opacity: 0.05,
            size: '300px'
        },
        margins: {
            top: '120px',
            bottom: '80px',
            left: '20px',
            right: '20px'
        }
    },

    // Navigation settings
    navigation: {
        logoSize: 'md',
        logoVariant: 'default',
        showOnMobile: true,
        hoverEffect: 'opacity'
    },

    // Theme settings
    themes: {
        light: {
            logo: 'default',
            navBg: 'white',
            textColor: 'blue-600'
        },
        dark: {
            logo: 'white',
            navBg: 'gray-900',
            textColor: 'white'
        }
    },

    // Responsive breakpoints
    responsive: {
        mobile: {
            logoSize: 'sm',
            hideText: true
        },
        tablet: {
            logoSize: 'md',
            hideText: false
        },
        desktop: {
            logoSize: 'md',
            hideText: false
        }
    },

    // Animation settings
    animations: {
        hover: {
            enabled: true,
            type: 'opacity', // 'opacity', 'scale', 'rotate'
            duration: '200ms'
        },
        loading: {
            enabled: true,
            type: 'pulse'
        }
    },

    // Accessibility
    accessibility: {
        altText: 'Season Expo',
        altTextAr: 'منصة المعارض',
        ariaLabel: 'Season Expo Platform Logo',
        ariaLabelAr: 'شعار منصة المعارض'
    },

    // Helper functions
    getLogo: function(variant = 'default') {
        return this.paths[variant] || this.paths.default;
    },

    getLogoSize: function(size = 'md') {
        return this.sizes[size] || this.sizes.md;
    },

    getPlatformName: function(lang = 'ar') {
        return lang === 'ar' ? this.platform.name : this.platform.nameEn;
    },

    getTagline: function(lang = 'ar') {
        return lang === 'ar' ? this.platform.tagline : this.platform.taglineEn;
    },

    getCopyright: function(lang = 'ar') {
        const year = new Date().getFullYear();
        const text = lang === 'ar' ? this.platform.copyright : this.platform.copyrightEn;
        return `© ${year} ${text}`;
    },

    // Generate logo HTML
    generateLogoHTML: function(options = {}) {
        const {
            variant = 'default',
            size = 'md',
            href = '/',
            className = '',
            alt = null,
            showText = false,
            textClass = 'text-2xl font-bold text-blue-600'
        } = options;

        const logoSrc = this.getLogo(variant);
        const logoSize = this.getLogoSize(size);
        const altText = alt || this.accessibility.altText;

        let html = `<a href="${href}" class="hover:opacity-80 transition-opacity ${className}">`;
        
        if (showText) {
            html += `
                <div class="flex items-center space-x-2 space-x-reverse">
                    <img src="${logoSrc}" alt="${altText}" class="${logoSize} w-auto">
                    <span class="${textClass}">${this.getPlatformName()}</span>
                </div>
            `;
        } else {
            html += `<img src="${logoSrc}" alt="${altText}" class="${logoSize} w-auto">`;
        }
        
        html += '</a>';
        return html;
    },

    // Generate print header HTML
    generatePrintHeaderHTML: function() {
        if (!this.print.header.enabled) return '';

        const logoSrc = this.getLogo('default');
        const platformName = this.getPlatformName();
        const tagline = this.getTagline();
        const url = this.platform.url;
        const date = new Date().toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
            <div class="print-header print-only" style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #333;">
                <img src="${logoSrc}" alt="${this.accessibility.altText}" style="max-height: ${this.print.header.logoHeight}; width: auto; margin-bottom: 10px;">
                ${this.print.header.showTitle ? `<div style="font-size: 18pt; font-weight: bold; margin: 10px 0; color: #333;">${platformName}</div>` : ''}
                <div style="font-size: 12pt; color: #666; margin: 5px 0;">${tagline}</div>
                ${this.print.header.showUrl ? `<div style="font-size: 12pt; color: #666; margin: 5px 0;">${url}</div>` : ''}
                ${this.print.header.showDate ? `<div style="font-size: 10pt; color: #666; margin: 10px 0;">تاريخ الطباعة: ${date}</div>` : ''}
            </div>
        `;
    },

    // Generate print footer HTML
    generatePrintFooterHTML: function() {
        if (!this.print.footer.enabled) return '';

        const logoSrc = this.getLogo('small');
        const copyright = this.getCopyright();
        const url = this.platform.url;

        return `
            <div class="print-footer print-only" style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc; font-size: 10pt; color: #666;">
                <img src="${logoSrc}" alt="${this.accessibility.altText}" style="max-height: ${this.print.footer.logoHeight}; width: auto; margin-bottom: 10px;">
                ${this.print.footer.showCopyright ? `<div>${copyright}</div>` : ''}
                ${this.print.footer.showUrl ? `<div>${url}</div>` : ''}
            </div>
        `;
    },

    // Initialize logo configuration
    init: function() {
        // Add CSS classes for logo sizes
        const style = document.createElement('style');
        style.textContent = `
            .logo-xs { height: 1.5rem; }
            .logo-sm { height: 2rem; }
            .logo-md { height: 2.5rem; }
            .logo-lg { height: 3rem; }
            .logo-xl { height: 4rem; }
            .logo-2xl { height: 5rem; }
            .logo-3xl { height: 6rem; }
            
            .logo-hover-opacity:hover { opacity: 0.8; transition: opacity 200ms; }
            .logo-hover-scale:hover { transform: scale(1.05); transition: transform 200ms; }
            .logo-hover-rotate:hover { transform: rotate(5deg); transition: transform 200ms; }
            
            @media print {
                .logo-print-header { display: block !important; }
                .logo-print-footer { display: block !important; }
            }
        `;
        document.head.appendChild(style);

        // Set favicon
        this.setFavicon();
        
        console.log('Logo configuration initialized');
    },

    // Set favicon and app icons
    setFavicon: function() {
        // Remove existing favicons
        const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
        existingFavicons.forEach(favicon => favicon.remove());

        // Add new favicons
        const favicons = [
            { rel: 'icon', type: 'image/png', sizes: '32x32', href: this.paths.favicon },
            { rel: 'apple-touch-icon', sizes: '180x180', href: this.paths.appleTouchIcon },
            { rel: 'icon', type: 'image/png', sizes: '192x192', href: this.paths.androidChrome192 },
            { rel: 'icon', type: 'image/png', sizes: '512x512', href: this.paths.androidChrome512 }
        ];

        favicons.forEach(favicon => {
            const link = document.createElement('link');
            Object.keys(favicon).forEach(key => {
                link.setAttribute(key, favicon[key]);
            });
            document.head.appendChild(link);
        });
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.LogoConfig.init();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.LogoConfig;
}
