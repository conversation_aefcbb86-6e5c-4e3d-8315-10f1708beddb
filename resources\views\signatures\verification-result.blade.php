<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>نتيجة التحقق من التوقيع - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        @media print {
            .no-print { display: none; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600">Season Expo</a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                    <a href="/signatures" class="bg-emerald-500 text-white px-3 py-1 rounded font-bold hover:bg-emerald-600">✍️ التوقيعات الرقمية</a>
                    <a href="/signatures/verify-simple" class="bg-purple-500 text-white px-3 py-1 rounded font-bold hover:bg-purple-600">🔍 تحقق من توقيع</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="text-6xl mb-4 pulse-animation">✅</div>
            <h1 class="text-4xl font-bold text-green-600 mb-2">تم التحقق من التوقيع بنجاح!</h1>
            <p class="text-xl text-gray-600">التوقيع الرقمي صحيح وموثق</p>
        </div>

        <!-- Verification Result -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
            <!-- Success Banner -->
            <div class="bg-green-500 text-white p-6 text-center">
                <div class="text-4xl mb-2">🎉</div>
                <h2 class="text-2xl font-bold">التوقيع صحيح ومعتمد</h2>
                <p class="text-green-100 mt-2">هذا المستند موقع رقمياً وموثق من قبل Season Expo</p>
            </div>

            <!-- Signature Details -->
            <div class="p-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">📋 تفاصيل التوقيع الرقمي</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Info -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">رقم التوقيع</label>
                            <p class="text-lg font-bold text-blue-600">#{{ $signature->id }}</p>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">نوع المستند</label>
                            <p class="text-lg font-semibold text-gray-900">
                                @if($signature->document_type === 'agreement')
                                    📄 مستند الشروط والأحكام
                                @elseif($signature->document_type === 'company_declaration')
                                    🏢 إقرار وتعهد الشركة المنظمة
                                @elseif($signature->document_type === 'exhibitor_declaration')
                                    🎪 إقرار وتعهد العارض
                                @else
                                    📋 {{ $signature->document_type }}
                                @endif
                            </p>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">رقم المستند</label>
                            <p class="text-lg font-mono text-gray-900">{{ $signature->document_id }}</p>
                        </div>
                    </div>

                    <!-- Signer Info -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">اسم الموقع</label>
                            <p class="text-lg font-bold text-gray-900">{{ $signature->signer_name }}</p>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">البريد الإلكتروني</label>
                            <p class="text-lg text-gray-900">{{ $signature->signer_email }}</p>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">عنوان IP</label>
                            <p class="text-lg font-mono text-gray-900">{{ $signature->signer_ip }}</p>
                        </div>
                    </div>
                </div>

                <!-- Timestamps -->
                <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <label class="block text-sm font-medium text-blue-600 mb-1">📅 تاريخ التوقيع</label>
                        <p class="text-lg font-bold text-blue-900">{{ $signature->signed_at->format('d/m/Y H:i:s') }}</p>
                        <p class="text-sm text-blue-600 mt-1">{{ $signature->signed_at->diffForHumans() }}</p>
                    </div>
                    
                    @if($signature->verified_at)
                    <div class="bg-green-50 rounded-lg p-4">
                        <label class="block text-sm font-medium text-green-600 mb-1">✅ تاريخ التحقق</label>
                        <p class="text-lg font-bold text-green-900">{{ $signature->verified_at->format('d/m/Y H:i:s') }}</p>
                        <p class="text-sm text-green-600 mt-1">{{ $signature->verified_at->diffForHumans() }}</p>
                    </div>
                    @endif
                </div>

                <!-- Verification Token -->
                <div class="mt-8 bg-purple-50 rounded-lg p-6">
                    <label class="block text-sm font-medium text-purple-600 mb-2">🔑 رمز التحقق</label>
                    <p class="text-lg font-mono text-purple-900 bg-white p-3 rounded border break-all">{{ $signature->verification_token }}</p>
                    <p class="text-sm text-purple-600 mt-2">يمكن استخدام هذا الرمز للتحقق من التوقيع مرة أخرى</p>
                </div>

                <!-- Security Hash -->
                <div class="mt-6 bg-gray-50 rounded-lg p-6">
                    <label class="block text-sm font-medium text-gray-600 mb-2">🔒 بصمة الأمان (Hash)</label>
                    <p class="text-sm font-mono text-gray-700 bg-white p-3 rounded border break-all">{{ $signature->signature_hash }}</p>
                    <p class="text-xs text-gray-500 mt-2">هذه البصمة تضمن عدم تعديل المستند بعد التوقيع</p>
                </div>

                <!-- Metadata -->
                @if($signature->metadata)
                <div class="mt-6 bg-yellow-50 rounded-lg p-6">
                    <label class="block text-sm font-medium text-yellow-600 mb-2">📊 معلومات إضافية</label>
                    <div class="bg-white p-3 rounded border">
                        @foreach($signature->metadata as $key => $value)
                            @if(is_string($value) || is_numeric($value))
                                <div class="flex justify-between py-1 border-b border-gray-100 last:border-b-0">
                                    <span class="text-sm font-medium text-gray-600">{{ $key }}:</span>
                                    <span class="text-sm text-gray-900">{{ $value }}</span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Actions -->
        <div class="text-center space-x-reverse space-x-4 no-print">
            <button onclick="window.print()" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
                🖨️ طباعة النتيجة
            </button>
            
            @if($signature->certificate_path)
                <a href="/signatures/{{ $signature->id }}/view" class="bg-green-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-green-700 inline-block">
                    📄 عرض المستند الأصلي
                </a>
            @endif
            
            <a href="/signatures/verify-simple" class="bg-purple-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-purple-700 inline-block">
                🔍 تحقق من توقيع آخر
            </a>
            
            <a href="/signatures" class="bg-emerald-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-emerald-700 inline-block">
                ✍️ التوقيعات الرقمية
            </a>
        </div>

        <!-- Security Notice -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex">
                <div class="text-2xl mr-3">🛡️</div>
                <div>
                    <h3 class="text-lg font-medium text-blue-800 mb-2">ضمان الأمان والموثوقية</h3>
                    <p class="text-sm text-blue-700">
                        هذا التوقيع الرقمي محمي بتشفير SHA-256 ومطابق للمعايير الدولية للتوقيعات الإلكترونية. 
                        جميع البيانات محفوظة بأمان ولا يمكن تعديلها أو تزويرها.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12 no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-lg font-semibold mb-2">Season Expo - منصة المعارض الرقمية</p>
            <p class="text-gray-400">© 2025 جميع الحقوق محفوظة</p>
        </div>
    </footer>
</body>
</html>
