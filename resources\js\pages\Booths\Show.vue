<script setup lang="ts">
import { Head, Link, useForm } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';

interface Exhibition {
  id: number;
  title: string;
  slug: string;
  city: string;
  country: string;
  start_date: string;
  end_date: string;
  currency: string;
  category: {
    name: string;
    color: string;
  };
  organizer: {
    name: string;
    company: string;
  };
}

interface Booth {
  id: number;
  booth_number: string;
  name: string;
  description: string;
  size: string;
  width: number;
  height: number;
  area: number;
  price: number;
  location: string;
  features: string[];
  status: string;
  is_featured: boolean;
  is_corner: boolean;
  image: string | null;
}

interface Props {
  exhibition: Exhibition;
  booth: Booth;
  similarBooths: Booth[];
}

const props = defineProps<Props>();

const bookingForm = useForm({
  booth_id: props.booth.id,
  exhibition_id: props.exhibition.id,
});

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatPrice = (price: number, currency: string) => {
  // Always use KWD for display
  if (currency === 'USD' || currency === 'KWD') {
    // Format as KWD with 3 decimal places
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'KWD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(price);
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  }).format(price);
};

const getSizeColor = (size: string) => {
  const colors = {
    small: 'bg-green-100 text-green-800',
    medium: 'bg-blue-100 text-blue-800',
    large: 'bg-purple-100 text-purple-800',
    premium: 'bg-yellow-100 text-yellow-800'
  };
  return colors[size as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};

const getStatusColor = (status: string) => {
  const colors = {
    available: 'bg-green-100 text-green-800',
    booked: 'bg-red-100 text-red-800',
    reserved: 'bg-yellow-100 text-yellow-800',
    maintenance: 'bg-gray-100 text-gray-800'
  };
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};

const bookBooth = () => {
  bookingForm.post(route('booths.book', [props.exhibition.slug, props.booth.id]), {
    onSuccess: () => {
      // Handle success
    },
    onError: () => {
      // Handle error
    }
  });
};
</script>

<template>
  <Head :title="`جناح ${booth.booth_number} - ${exhibition.title}`" />

  <AppLayout>
    <div class="container mx-auto px-4 py-8">
      <!-- Breadcrumb -->
      <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li><Link :href="route('home')" class="hover:text-blue-600">Home</Link></li>
          <li>/</li>
          <li><Link :href="route('exhibitions.index')" class="hover:text-blue-600">Exhibitions</Link></li>
          <li>/</li>
          <li><Link :href="route('exhibitions.show', exhibition.slug)" class="hover:text-blue-600">{{ exhibition.title }}</Link></li>
          <li>/</li>
          <li><Link :href="route('booths.index', exhibition.slug)" class="hover:text-blue-600">Booths</Link></li>
          <li>/</li>
          <li class="text-gray-900">{{ booth.booth_number }}</li>
        </ol>
      </nav>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
          <!-- Booth Header -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div class="h-64 bg-gray-200 relative">
              <img
                v-if="booth.image"
                :src="booth.image"
                :alt="`Booth ${booth.booth_number}`"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-4 left-4 flex space-x-2">
                <span :class="['px-3 py-1 rounded-full text-sm font-medium', getSizeColor(booth.size)]">
                  {{ booth.size }}
                </span>
                <span v-if="booth.is_corner" class="px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                  Corner Booth
                </span>
                <span v-if="booth.is_featured" class="px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                  Featured
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span :class="['px-3 py-1 rounded-full text-sm font-medium', getStatusColor(booth.status)]">
                  {{ booth.status }}
                </span>
              </div>
            </div>

            <div class="p-8">
              <div class="flex items-start justify-between mb-6">
                <div>
                  <h1 class="text-3xl font-bold text-gray-900 mb-2">Booth {{ booth.booth_number }}</h1>
                  <h2 v-if="booth.name" class="text-xl text-gray-600 mb-4">{{ booth.name }}</h2>
                  <div class="text-3xl font-bold text-blue-600">
                    {{ formatPrice(booth.price, exhibition.currency) }}
                  </div>
                </div>
              </div>

              <!-- Booth Details -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">{{ booth.width }}m</div>
                  <div class="text-sm text-gray-600">Width</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">{{ booth.height }}m</div>
                  <div class="text-sm text-gray-600">Height</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">{{ booth.area }}m²</div>
                  <div class="text-sm text-gray-600">Total Area</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 capitalize">{{ booth.size }}</div>
                  <div class="text-sm text-gray-600">Size</div>
                </div>
              </div>

              <!-- Location -->
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Location</h3>
                <div class="flex items-center text-gray-600">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  {{ booth.location }}
                </div>
              </div>

              <!-- Features -->
              <div v-if="booth.features.length > 0" class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Included Features</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                  <div
                    v-for="feature in booth.features"
                    :key="feature"
                    class="flex items-center text-sm text-gray-600"
                  >
                    <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    {{ feature.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) }}
                  </div>
                </div>
              </div>

              <!-- Description -->
              <div v-if="booth.description" class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                <p class="text-gray-600">{{ booth.description }}</p>
              </div>
            </div>
          </div>

          <!-- Exhibition Info -->
          <div class="bg-white rounded-lg shadow-md p-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Exhibition Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-medium text-gray-900 mb-2">{{ exhibition.title }}</h4>
                <div class="text-sm text-gray-600 space-y-1">
                  <div>📍 {{ exhibition.city }}, {{ exhibition.country }}</div>
                  <div>📅 {{ formatDate(exhibition.start_date) }} - {{ formatDate(exhibition.end_date) }}</div>
                  <div>🏢 {{ exhibition.organizer.name }}</div>
                </div>
              </div>
              <div>
                <span
                  class="inline-block px-3 py-1 rounded-full text-sm font-medium text-white mb-2"
                  :style="{ backgroundColor: exhibition.category.color }"
                >
                  {{ exhibition.category.name }}
                </span>
                <div class="text-sm text-gray-600">
                  {{ exhibition.organizer.company }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Booking Card -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8 sticky top-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Book This Booth</h3>

            <div class="mb-6">
              <div class="text-3xl font-bold text-blue-600 mb-2">
                {{ formatPrice(booth.price, exhibition.currency) }}
              </div>
              <div class="text-sm text-gray-600">Total booth cost</div>
            </div>

            <div v-if="booth.status === 'available'" class="space-y-4">
              <button
                @click="bookBooth"
                :disabled="bookingForm.processing"
                class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold disabled:opacity-50"
              >
                <span v-if="bookingForm.processing">Processing...</span>
                <span v-else>Book Now</span>
              </button>

              <div class="text-xs text-gray-500 text-center">
                You'll be redirected to complete your booking
              </div>
            </div>

            <div v-else-if="booth.status === 'booked'" class="text-center">
              <div class="text-red-600 font-medium mb-2">This booth is already booked</div>
              <Link
                :href="route('booths.index', exhibition.slug)"
                class="text-blue-600 hover:text-blue-800 text-sm"
              >
                View other available booths →
              </Link>
            </div>

            <div v-else class="text-center">
              <div class="text-yellow-600 font-medium mb-2">This booth is not available</div>
              <div class="text-sm text-gray-600 capitalize">Status: {{ booth.status }}</div>
            </div>
          </div>

          <!-- Contact Info -->
          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-3">Need Help?</h4>
            <div class="text-sm text-gray-600 space-y-2">
              <div>Contact the exhibition organizer for more information about this booth.</div>
              <div class="font-medium">{{ exhibition.organizer.name }}</div>
              <div>{{ exhibition.organizer.company }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Similar Booths -->
      <div v-if="similarBooths.length > 0" class="mt-12">
        <h3 class="text-2xl font-bold text-gray-900 mb-6">Similar Booths</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link
            v-for="similar in similarBooths"
            :key="similar.id"
            :href="route('booths.show', [exhibition.slug, similar.id])"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            <div class="p-4">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-semibold text-gray-900">{{ similar.booth_number }}</h4>
                <span :class="['px-2 py-1 rounded-full text-xs font-medium', getSizeColor(similar.size)]">
                  {{ similar.size }}
                </span>
              </div>
              <div class="text-sm text-gray-600 mb-2">
                {{ similar.width }}m × {{ similar.height }}m ({{ similar.area }}m²)
              </div>
              <div class="text-sm text-gray-600 mb-3">
                📍 {{ similar.location }}
              </div>
              <div class="text-lg font-bold text-blue-600">
                {{ formatPrice(similar.price, exhibition.currency) }}
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
