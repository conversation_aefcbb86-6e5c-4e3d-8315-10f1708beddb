<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
  featuredExhibitions: {
    type: Array,
    default: () => []
  },
  upcomingExhibitions: {
    type: Array,
    default: () => []
  },
  categories: {
    type: Array,
    default: () => []
  },
  stats: {
    type: Object,
    default: () => ({
      total_exhibitions: 0,
      upcoming_exhibitions: 0,
      total_booths: 0,
      available_booths: 0,
    })
  },
  heroSlides: {
    type: Array,
    default: () => []
  },
  exhibitionImages: {
    type: Array,
    default: () => []
  },
  locale: {
    type: String,
    default: 'ar'
  },
  translations: {
    type: Object,
    default: () => ({})
  }
});

const t = (key) => {
  return props.translations.app?.[key] || key;
};

// Format price in KWD
const formatKWD = (amount) => {
  return parseFloat(amount).toFixed(3);
};

const formatPrice = (price, currency) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';

  // Always use KWD for display
  if (currency === 'USD' || currency === 'KWD') {
    // Format as KWD with 3 decimal places
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'KWD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(price);
  }

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  }).format(price);
};

const formatDate = (dateString) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';
  return new Date(dateString).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const isRTL = props.locale === 'ar';

// Hero slider data - use database images if available, otherwise fallback to default
const getHeroSlides = () => {
  if (props.heroSlides && props.heroSlides.length > 0) {
    return props.heroSlides.map(slide => ({
      id: slide.id,
      image: slide.url,
      fallbackImage: 'https://via.placeholder.com/1920x1080/4F46E5/FFFFFF?text=' + encodeURIComponent(slide.name),
      title: slide.title || slide.name,
      subtitle: slide.description || (isRTL ? 'اكتشف المعارض والفعاليات المميزة' : 'Discover amazing exhibitions and events'),
      location: slide.metadata?.location || (isRTL ? 'الشرق الأوسط' : 'Middle East'),
      date: slide.metadata?.date || (isRTL ? 'قريباً' : 'Coming Soon')
    }));
  }

  // Fallback data
  return [
    {
      id: 1,
      image: 'https://picsum.photos/1920/1080?random=1',
      fallbackImage: 'https://via.placeholder.com/1920x1080/4F46E5/FFFFFF?text=Tech+Innovation+Expo+2024',
      title: isRTL ? 'معرض التكنولوجيا والابتكار 2024' : 'Tech Innovation Expo 2024',
      subtitle: isRTL ? 'اكتشف أحدث التقنيات والابتكارات في دبي' : 'Discover the latest technologies and innovations in Dubai',
      location: isRTL ? 'دبي، الإمارات العربية المتحدة' : 'Dubai, UAE',
      date: isRTL ? '15-18 ديسمبر 2024' : 'Dec 15-18, 2024'
    },
    {
      id: 2,
      image: 'https://picsum.photos/1920/1080?random=2',
      fallbackImage: 'https://via.placeholder.com/1920x1080/059669/FFFFFF?text=Healthcare+Medical+Expo',
      title: isRTL ? 'معرض الرعاية الصحية والطبية' : 'Healthcare & Medical Expo',
      subtitle: isRTL ? 'أحدث الحلول الطبية والرعاية الصحية' : 'Latest medical solutions and healthcare innovations',
      location: isRTL ? 'الكويت، الكويت' : 'Kuwait City, Kuwait',
      date: isRTL ? '20-23 يناير 2025' : 'Jan 20-23, 2025'
    },
    {
      id: 3,
      image: 'https://picsum.photos/1920/1080?random=3',
      fallbackImage: 'https://via.placeholder.com/1920x1080/EA580C/FFFFFF?text=Food+Beverage+Festival',
      title: isRTL ? 'مهرجان الأغذية والمشروبات' : 'Food & Beverage Festival',
      subtitle: isRTL ? 'تذوق أفضل المأكولات والمشروبات العالمية' : 'Taste the finest global cuisines and beverages',
      location: isRTL ? 'الرياض، السعودية' : 'Riyadh, Saudi Arabia',
      date: isRTL ? '5-8 فبراير 2025' : 'Feb 5-8, 2025'
    }
  ];
};

const heroSlides = getHeroSlides();

// Current slide index
const currentSlide = ref(0);

// Auto-slide functionality
const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % heroSlides.length;
};

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? heroSlides.length - 1 : currentSlide.value - 1;
};

const goToSlide = (index) => {
  currentSlide.value = index;
};

// Image error handling
const handleImageError = (event, slide) => {
  event.target.src = slide.fallbackImage;
};

// Auto-play slider
onMounted(() => {
  setInterval(nextSlide, 5000);
});
</script>

<template>
  <Head :title="t('site_name') + ' - ' + t('hero_title')" />

  <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
              {{ t('site_name') }}
            </Link>
          </div>
          <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
            <LanguageSwitcher :current-locale="locale" />
            <Link href="/exhibitions" class="nav-link text-gray-600">{{ t('exhibitions') }}</Link>
            <Link
              v-if="$page.props.auth?.user"
              :href="route('dashboard')"
              class="nav-link text-gray-600"
            >
              {{ t('dashboard') }}
            </Link>
            <template v-else>
              <Link
                :href="route('login')"
                class="nav-link text-gray-600"
              >
                {{ t('sign_in') }}
              </Link>
              <Link
                :href="route('register')"
                class="btn-primary bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {{ t('get_started') }}
              </Link>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Slider Section -->
    <section class="relative h-screen overflow-hidden pt-16">
      <div class="absolute inset-0">
        <div
          v-for="(slide, index) in heroSlides"
          :key="slide.id"
          class="hero-slide absolute inset-0 transition-opacity duration-1000"
          :class="{ 'opacity-100': index === currentSlide, 'opacity-0': index !== currentSlide }"
        >
          <img
            :src="slide.image"
            :alt="slide.title"
            class="w-full h-full object-cover"
            @error="handleImageError($event, slide)"
            loading="lazy"
          />
          <div class="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>
      </div>

      <!-- Slide Content -->
      <div class="relative z-10 h-full flex items-center">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div class="max-w-3xl" :class="isRTL ? 'mr-auto text-right' : 'ml-auto text-left'">
            <div class="text-white">
              <h1 class="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                {{ heroSlides[currentSlide].title }}
              </h1>
              <p class="text-xl md:text-2xl mb-8 text-gray-200">
                {{ heroSlides[currentSlide].subtitle }}
              </p>

              <!-- Event Details -->
              <div class="flex flex-col sm:flex-row gap-6 mb-8 text-lg">
                <div class="flex items-center">
                  <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  {{ heroSlides[currentSlide].location }}
                </div>
                <div class="flex items-center">
                  <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {{ heroSlides[currentSlide].date }}
                </div>
              </div>

              <!-- CTA Buttons -->
              <div class="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/exhibitions"
                  class="btn-primary bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-700"
                >
                  {{ t('browse_exhibitions') }}
                </Link>
                <Link
                  href="/search"
                  class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300"
                >
                  {{ t('search_booths') }}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Arrows -->
      <button
        @click="prevSlide"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300"
        :class="isRTL ? 'left-auto right-4' : 'left-4'"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isRTL ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'" />
        </svg>
      </button>

      <button
        @click="nextSlide"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300"
        :class="isRTL ? 'right-auto left-4' : 'right-4'"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isRTL ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'" />
        </svg>
      </button>

      <!-- Slide Indicators -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        <button
          v-for="(slide, index) in heroSlides"
          :key="index"
          @click="goToSlide(index)"
          class="w-3 h-3 rounded-full transition-all duration-300"
          :class="index === currentSlide ? 'bg-white' : 'bg-white bg-opacity-50'"
        ></button>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600 mb-2">{{ stats.total_exhibitions }}</div>
            <div class="text-gray-600">{{ t('total_exhibitions') }}</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{{ stats.upcoming_exhibitions }}</div>
            <div class="text-gray-600">{{ t('upcoming_exhibitions') }}</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600 mb-2">{{ stats.total_booths }}</div>
            <div class="text-gray-600">{{ t('total_booths') }}</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-orange-600 mb-2">{{ stats.available_booths }}</div>
            <div class="text-gray-600">{{ t('available_booths') }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-6">{{ t('exhibition_categories') }}</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            {{ t('exhibition_categories_subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          <!-- Technology -->
          <div class="text-center group cursor-pointer">
            <div class="w-20 h-20 mx-auto mb-4 bg-blue-100 rounded-2xl flex items-center justify-center group-hover:bg-blue-600 transition-all duration-300">
              <svg class="w-10 h-10 text-blue-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {{ isRTL ? 'التكنولوجيا' : 'Technology' }}
            </h3>
          </div>

          <!-- Healthcare -->
          <div class="text-center group cursor-pointer">
            <div class="w-20 h-20 mx-auto mb-4 bg-green-100 rounded-2xl flex items-center justify-center group-hover:bg-green-600 transition-all duration-300">
              <svg class="w-10 h-10 text-green-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
              {{ isRTL ? 'الرعاية الصحية' : 'Healthcare' }}
            </h3>
          </div>

          <!-- Food & Beverage -->
          <div class="text-center group cursor-pointer">
            <div class="w-20 h-20 mx-auto mb-4 bg-orange-100 rounded-2xl flex items-center justify-center group-hover:bg-orange-600 transition-all duration-300">
              <svg class="w-10 h-10 text-orange-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
              {{ isRTL ? 'الأغذية والمشروبات' : 'Food & Beverage' }}
            </h3>
          </div>

          <!-- Fashion -->
          <div class="text-center group cursor-pointer">
            <div class="w-20 h-20 mx-auto mb-4 bg-pink-100 rounded-2xl flex items-center justify-center group-hover:bg-pink-600 transition-all duration-300">
              <svg class="w-10 h-10 text-pink-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-pink-600 transition-colors">
              {{ isRTL ? 'الأزياء' : 'Fashion' }}
            </h3>
          </div>

          <!-- Automotive -->
          <div class="text-center group cursor-pointer">
            <div class="w-20 h-20 mx-auto mb-4 bg-red-100 rounded-2xl flex items-center justify-center group-hover:bg-red-600 transition-all duration-300">
              <svg class="w-10 h-10 text-red-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-red-600 transition-colors">
              {{ isRTL ? 'السيارات' : 'Automotive' }}
            </h3>
          </div>

          <!-- Education -->
          <div class="text-center group cursor-pointer">
            <div class="w-20 h-20 mx-auto mb-4 bg-purple-100 rounded-2xl flex items-center justify-center group-hover:bg-purple-600 transition-all duration-300">
              <svg class="w-10 h-10 text-purple-600 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
              {{ isRTL ? 'التعليم' : 'Education' }}
            </h3>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Exhibitions -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-6">{{ t('featured_exhibitions') }}</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            {{ t('featured_exhibitions_subtitle') }}
          </p>
        </div>

        <!-- Sample Featured Exhibitions with Images -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Tech Exhibition -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="tech-card relative h-64 overflow-hidden">
              <img
                src="https://picsum.photos/800/600?random=4"
                alt="Tech Innovation Expo"
                class="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                @error="$event.target.src='https://via.placeholder.com/800x600/4F46E5/FFFFFF?text=Tech+Innovation+Expo'"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 left-4">
                <span class="px-3 py-1 rounded-full text-sm font-medium text-white bg-blue-600">
                  {{ isRTL ? 'التكنولوجيا' : 'Technology' }}
                </span>
              </div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  {{ isRTL ? 'دبي، الإمارات' : 'Dubai, UAE' }}
                </div>
              </div>
            </div>

            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">
                {{ isRTL ? 'معرض التكنولوجيا والابتكار 2024' : 'Tech Innovation Expo 2024' }}
              </h3>
              <p class="text-gray-600 mb-4 line-clamp-2">
                {{ isRTL ? 'اكتشف أحدث التقنيات والابتكارات في مجال التكنولوجيا والذكاء الاصطناعي' : 'Discover the latest technologies and innovations in AI, IoT, and digital transformation' }}
              </p>

              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center text-sm text-gray-500">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {{ isRTL ? '15-18 ديسمبر' : 'Dec 15-18' }}
                </div>
                <div class="text-sm font-semibold text-blue-600">
                  {{ isRTL ? '200 جناح' : '200 Booths' }}
                </div>
              </div>

              <Link
                v-if="featuredExhibitions[0]"
                :href="route('exhibitions.show', featuredExhibitions[0].slug)"
                class="btn-primary w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 text-center block"
              >
                {{ t('view_details') }}
              </Link>
              <Link
                v-else
                href="/exhibitions"
                class="btn-primary w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 text-center block"
              >
                {{ t('view_details') }}
              </Link>
            </div>
          </div>

          <!-- Healthcare Exhibition -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="healthcare-card relative h-64 overflow-hidden">
              <img
                src="https://picsum.photos/800/600?random=5"
                alt="Healthcare Expo"
                class="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                @error="$event.target.src='https://via.placeholder.com/800x600/059669/FFFFFF?text=Healthcare+Medical+Expo'"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 left-4">
                <span class="px-3 py-1 rounded-full text-sm font-medium text-white bg-green-600">
                  {{ isRTL ? 'الرعاية الصحية' : 'Healthcare' }}
                </span>
              </div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  {{ isRTL ? 'الكويت، الكويت' : 'Kuwait City, Kuwait' }}
                </div>
              </div>
            </div>

            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">
                {{ isRTL ? 'معرض الرعاية الصحية والطبية' : 'Healthcare & Medical Expo' }}
              </h3>
              <p class="text-gray-600 mb-4 line-clamp-2">
                {{ isRTL ? 'أحدث الحلول الطبية والتقنيات الصحية المتطورة للمستقبل' : 'Latest medical solutions and advanced healthcare technologies for the future' }}
              </p>

              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center text-sm text-gray-500">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {{ isRTL ? '20-23 يناير' : 'Jan 20-23' }}
                </div>
                <div class="text-sm font-semibold text-green-600">
                  {{ isRTL ? '150 جناح' : '150 Booths' }}
                </div>
              </div>

              <Link
                v-if="featuredExhibitions[1]"
                :href="route('exhibitions.show', featuredExhibitions[1].slug)"
                class="btn-primary w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 text-center block"
              >
                {{ t('view_details') }}
              </Link>
              <Link
                v-else
                href="/exhibitions"
                class="btn-primary w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 text-center block"
              >
                {{ t('view_details') }}
              </Link>
            </div>
          </div>

          <!-- Food & Beverage Exhibition -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="food-card relative h-64 overflow-hidden">
              <img
                src="https://picsum.photos/800/600?random=6"
                alt="Food & Beverage Festival"
                class="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                @error="$event.target.src='https://via.placeholder.com/800x600/EA580C/FFFFFF?text=Food+Beverage+Festival'"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute top-4 left-4">
                <span class="px-3 py-1 rounded-full text-sm font-medium text-white bg-orange-600">
                  {{ isRTL ? 'الأغذية والمشروبات' : 'Food & Beverage' }}
                </span>
              </div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  {{ isRTL ? 'الرياض، السعودية' : 'Riyadh, Saudi Arabia' }}
                </div>
              </div>
            </div>

            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">
                {{ isRTL ? 'مهرجان الأغذية والمشروبات' : 'Food & Beverage Festival' }}
              </h3>
              <p class="text-gray-600 mb-4 line-clamp-2">
                {{ isRTL ? 'تذوق أفضل المأكولات والمشروبات العالمية من أشهر الطهاة' : 'Taste the finest global cuisines and beverages from renowned chefs' }}
              </p>

              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center text-sm text-gray-500">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {{ isRTL ? '5-8 فبراير' : 'Feb 5-8' }}
                </div>
                <div class="text-sm font-semibold text-orange-600">
                  {{ isRTL ? '120 جناح' : '120 Booths' }}
                </div>
              </div>

              <Link
                v-if="featuredExhibitions[2]"
                :href="route('exhibitions.show', featuredExhibitions[2].slug)"
                class="btn-primary w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-700 text-center block"
              >
                {{ t('view_details') }}
              </Link>
              <Link
                v-else
                href="/exhibitions"
                class="btn-primary w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-700 text-center block"
              >
                {{ t('view_details') }}
              </Link>
            </div>
          </div>
        </div>

        <!-- View All Button -->
        <div class="text-center mt-12">
          <Link
            href="/exhibitions"
            class="btn-primary bg-gray-900 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-800 inline-flex items-center"
          >
            {{ t('view_all_exhibitions') }}
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isRTL ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'" />
            </svg>
          </Link>
        </div>
      </div>
    </section>

    <!-- Welcome Message if no data -->
    <section v-if="!featuredExhibitions.length && !categories.length" class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 text-center">
        <div class="flex justify-center mb-4">
          <img src="/images/logo.png" alt="Season Expo" class="h-16 w-auto">
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">مرحباً بك في منصتنا!</h2>
        <p class="text-gray-600 mb-8">
          Your exhibition and booth booking platform is ready. The database has been set up with sample data.
        </p>
        <div class="flex justify-center space-x-4">
          <Link href="/exhibitions" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            View All Exhibitions
          </Link>
          <Link href="/dashboard" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
            Go to Dashboard
          </Link>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
      <div class="absolute inset-0 bg-black bg-opacity-20"></div>
      <div class="relative z-10 max-w-7xl mx-auto px-4 text-center">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">{{ t('ready_to_showcase') }}</h2>
        <p class="text-xl md:text-2xl mb-10 max-w-4xl mx-auto text-gray-100">
          {{ t('ready_to_showcase_subtitle') }}
        </p>

        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <Link
            href="/register"
            class="btn-primary bg-white text-blue-600 px-10 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 shadow-2xl"
          >
            {{ t('get_started') }}
          </Link>
          <Link
            href="/exhibitions"
            class="border-2 border-white text-white px-10 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300"
          >
            {{ t('browse_exhibitions') }}
          </Link>
        </div>

        <!-- Trust indicators -->
        <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div class="text-3xl font-bold mb-2">{{ stats.total_exhibitions }}+</div>
            <div class="text-gray-200">{{ t('total_exhibitions') }}</div>
          </div>
          <div>
            <div class="text-3xl font-bold mb-2">{{ stats.total_booths }}+</div>
            <div class="text-gray-200">{{ t('total_booths') }}</div>
          </div>
          <div>
            <div class="text-3xl font-bold mb-2">50+</div>
            <div class="text-gray-200">{{ isRTL ? 'مدينة' : 'Cities' }}</div>
          </div>
          <div>
            <div class="text-3xl font-bold mb-2">10K+</div>
            <div class="text-gray-200">{{ isRTL ? 'عارض راضٍ' : 'Happy Exhibitors' }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <!-- Company Info -->
          <div class="col-span-1 md:col-span-2">
            <h3 class="text-2xl font-bold text-blue-400 mb-4">{{ t('site_name') }}</h3>
            <p class="text-gray-300 mb-6 max-w-md">
              {{ isRTL ? 'منصة رائدة لحجز المعارض والأجنحة في الشرق الأوسط. نربط العارضين بأفضل الفرص التجارية.' : 'Leading exhibition and booth booking platform in the Middle East. Connecting exhibitors with the best business opportunities.' }}
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h4 class="text-lg font-semibold mb-4">{{ isRTL ? 'روابط سريعة' : 'Quick Links' }}</h4>
            <ul class="space-y-2">
              <li><Link href="/exhibitions" class="text-gray-300 hover:text-white transition-colors">{{ t('exhibitions') }}</Link></li>
              <li><Link href="/search" class="text-gray-300 hover:text-white transition-colors">{{ t('search_booths') }}</Link></li>
              <li><Link href="/register" class="text-gray-300 hover:text-white transition-colors">{{ t('register') }}</Link></li>
              <li><Link href="/login" class="text-gray-300 hover:text-white transition-colors">{{ t('login') }}</Link></li>
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h4 class="text-lg font-semibold mb-4">{{ isRTL ? 'الدعم' : 'Support' }}</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-300 hover:text-white transition-colors">{{ isRTL ? 'مركز المساعدة' : 'Help Center' }}</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white transition-colors">{{ isRTL ? 'اتصل بنا' : 'Contact Us' }}</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white transition-colors">{{ isRTL ? 'الأسئلة الشائعة' : 'FAQ' }}</a></li>
              <li><a href="#" class="text-gray-300 hover:text-white transition-colors">{{ isRTL ? 'سياسة الخصوصية' : 'Privacy Policy' }}</a></li>
            </ul>
          </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400">{{ t('copyright') }}</p>
          <div class="flex items-center mt-4 md:mt-0">
            <LanguageSwitcher :current-locale="locale" />
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
