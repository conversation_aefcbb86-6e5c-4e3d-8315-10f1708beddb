# Cache Control for Season Expo Kuwait
# Add this to your main .htaccess file

<IfModule mod_headers.c>
    # Disable caching for PHP files and dynamic content
    <FilesMatch "\.(php|html)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
    
    # Cache static assets but allow updates
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        Header set Cache-Control "public, max-age=3600"
        Header set Expires "access plus 1 hour"
    </FilesMatch>
    
    # Force revalidation for critical files
    <FilesMatch "booth-search">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# Prevent browser caching of dynamic pages
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Add version parameter to force refresh
    RewriteCond %{REQUEST_URI} ^/dashboard/booth-search$
    RewriteRule ^(.*)$ $1?v=20250104 [QSA,L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
