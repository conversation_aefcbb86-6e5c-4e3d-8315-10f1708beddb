<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>حجوزاتي - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-blue-600">Season Expo</a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/dashboard" class="text-gray-700 hover:text-blue-600">لوحة التحكم</a>
                    <a href="/my-bookings" class="text-blue-600 font-semibold">حجوزاتي</a>
                    <form method="POST" action="/logout" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-gray-700 hover:text-red-600">تسجيل الخروج</button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-8 px-4">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">📋 حجوزاتي</h1>
            <p class="text-gray-600">عرض وإدارة جميع حجوزات الأجنحة الخاصة بك</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <p><?php echo e($error); ?></p>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <!-- Stats Summary -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">إجمالي الحجوزات</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e($bookings->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">مدفوعة</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e($bookings->whereIn('status', ['paid', 'confirmed'])->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">في الانتظار</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e($bookings->where('status', 'pending')->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">إجمالي المبلغ</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e(number_format($bookings->sum('total_amount'), 3)); ?> KWD</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bookings List -->
        <?php if($bookings->count() > 0): ?>
            <div class="space-y-6">
                <?php $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900"><?php echo e($booking->exhibition->title); ?></h3>
                                    <p class="text-gray-600">جناح رقم <?php echo e($booking->booth->booth_number); ?></p>
                                </div>
                                <span class="px-3 py-1 text-sm font-medium rounded-full
                                    <?php echo e($booking->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                                    <?php echo e($booking->status === 'confirmed' ? 'bg-green-100 text-green-800' : ''); ?>

                                    <?php echo e($booking->status === 'paid' ? 'bg-green-100 text-green-800' : ''); ?>

                                    <?php echo e($booking->status === 'cancelled' ? 'bg-red-100 text-red-800' : ''); ?>">
                                    <?php echo e($booking->status === 'pending' ? 'في انتظار الدفع' : ''); ?>

                                    <?php echo e($booking->status === 'confirmed' ? 'مؤكد' : ''); ?>

                                    <?php echo e($booking->status === 'paid' ? 'مدفوع ✅' : ''); ?>

                                    <?php echo e($booking->status === 'cancelled' ? 'ملغي' : ''); ?>

                                </span>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <p class="text-sm text-gray-600">تاريخ الحجز</p>
                                    <p class="font-medium"><?php echo e($booking->created_at->format('d/m/Y H:i')); ?></p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">المبلغ</p>
                                    <p class="font-medium"><?php echo e(number_format($booking->total_amount, 3)); ?> <?php echo e($booking->currency); ?></p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">حالة الدفع</p>
                                    <?php if($booking->payments->isNotEmpty()): ?>
                                        <?php $payment = $booking->payments->first() ?>
                                        <p class="font-medium
                                            <?php echo e($payment->status === 'pending' ? 'text-yellow-600' : ''); ?>

                                            <?php echo e($payment->status === 'completed' ? 'text-green-600' : ''); ?>

                                            <?php echo e($payment->status === 'failed' ? 'text-red-600' : ''); ?>">
                                            <?php echo e($payment->status === 'pending' ? 'في انتظار الدفع' : ''); ?>

                                            <?php echo e($payment->status === 'completed' ? 'مدفوع ✅' : ''); ?>

                                            <?php echo e($payment->status === 'failed' ? 'فشل الدفع ❌' : ''); ?>

                                        </p>
                                    <?php else: ?>
                                        <p class="font-medium text-yellow-600">لم يتم الدفع بعد</p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Payment Details -->
                            <?php if($booking->payments->isNotEmpty() && $booking->payments->first()->status === 'completed'): ?>
                                <?php $payment = $booking->payments->first() ?>
                                <div class="bg-green-50 rounded-lg p-4 mb-4">
                                    <h4 class="font-semibold text-green-900 mb-2">✅ تفاصيل الدفع</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <?php if($payment->paid_at): ?>
                                            <div>
                                                <span class="text-green-700">تاريخ الدفع:</span>
                                                <span class="font-medium"><?php echo e($payment->paid_at->format('d/m/Y H:i')); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($payment->transaction_id): ?>
                                            <div>
                                                <span class="text-green-700">رقم المعاملة:</span>
                                                <span class="font-medium"><?php echo e($payment->transaction_id); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($payment->payment_method): ?>
                                            <div>
                                                <span class="text-green-700">طريقة الدفع:</span>
                                                <span class="font-medium">
                                                    <?php echo e($payment->payment_method === 'myfatoorah' ? 'MyFatoorah - K-Net' : $payment->payment_method); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($payment->gateway): ?>
                                            <div>
                                                <span class="text-green-700">البوابة:</span>
                                                <span class="font-medium"><?php echo e($payment->gateway); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Actions -->
                            <div class="flex space-x-reverse space-x-3">
                                <a href="/bookings/<?php echo e($booking->id); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    عرض التفاصيل
                                </a>
                                
                                <?php if($booking->status === 'pending'): ?>
                                    <form method="POST" action="/payment/initiate/<?php echo e($booking->id); ?>" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                            💳 ادفع الآن
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حجوزات بعد</h3>
                <p class="text-gray-600 mb-6">ابدأ بحجز جناح في أحد المعارض المتاحة</p>
                <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    تصفح المعارض
                </a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/bookings/my-bookings.blade.php ENDPATH**/ ?>