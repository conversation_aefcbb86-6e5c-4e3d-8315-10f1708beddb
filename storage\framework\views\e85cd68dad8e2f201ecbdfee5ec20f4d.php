<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إدارة أجنحة <?php echo e($exhibition->title ?? 'المعرض'); ?> - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                    <span class="mr-4 px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded">Admin</span>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/admin/exhibitions" class="text-gray-600 hover:text-gray-900">إدارة المعارض</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">إدارة أجنحة المعرض</h1>
                        <p class="text-gray-600 mt-2"><?php echo e($exhibition->title ?? 'معرض التكنولوجيا والابتكار 2024'); ?></p>
                    </div>
                    <a href="/admin/exhibitions" class="text-blue-600 hover:text-blue-700">← العودة للمعارض</a>
                </div>
            </div>

            <!-- Exhibition Info -->
            <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <div class="text-sm text-blue-600 font-medium">الموقع</div>
                        <div class="text-blue-900"><?php echo e($exhibition->location ?? 'مركز الكويت الدولي للمعارض'); ?></div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">تاريخ البداية</div>
                        <div class="text-blue-900"><?php echo e(isset($exhibition->start_date) ? $exhibition->start_date->format('d/m/Y') : '01/01/2024'); ?></div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">تاريخ النهاية</div>
                        <div class="text-blue-900"><?php echo e(isset($exhibition->end_date) ? $exhibition->end_date->format('d/m/Y') : '05/01/2024'); ?></div>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">الحالة</div>
                        <div class="text-blue-900"><?php echo e($exhibition->status ?? 'نشط'); ?></div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mb-8 flex flex-wrap gap-4">
                <a href="/admin/exhibitions/<?php echo e($exhibition->id ?? 1); ?>/booths/create"
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    ➕ إضافة جناح جديد
                </a>
                <a href="/admin/exhibitions/<?php echo e($exhibition->id ?? 1); ?>/booths/bulk-create"
                   class="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    🏢 إضافة أجنحة متعددة
                </a>
                <a href="/admin/exhibitions/<?php echo e($exhibition->id ?? 1); ?>/layout"
                   class="bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
                    🗺️ تخطيط المعرض
                </a>
            </div>

            <!-- Booths Grid -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-900">أجنحة المعرض</h2>
                        <div class="text-sm text-gray-500">
                            إجمالي: <?php echo e(count($booths ?? [])); ?> جناح
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        <?php $__empty_1 = true; $__currentLoopData = $booths ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booth): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="border-2 rounded-lg p-4 text-center transition-all hover:shadow-md
                                <?php echo e(($booth->status ?? 'available') === 'available' ? 'border-green-300 bg-green-50' :
                                   (($booth->status ?? 'available') === 'booked' ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-gray-50')); ?>">

                                <!-- Booth Number -->
                                <div class="text-lg font-bold mb-2
                                    <?php echo e(($booth->status ?? 'available') === 'available' ? 'text-green-800' :
                                       (($booth->status ?? 'available') === 'booked' ? 'text-red-800' : 'text-gray-800')); ?>">
                                    جناح <?php echo e($booth->booth_number ?? 'A-' . (($loop->index ?? 0) + 1)); ?>

                                </div>

                                <!-- Booth Info -->
                                <div class="text-sm text-gray-600 mb-3">
                                    <div><?php echo e($booth->size ?? 25); ?> م²</div>
                                    <div><?php echo e($booth->price ?? 500); ?> د.ك</div>
                                </div>

                                <!-- Status -->
                                <div class="text-xs font-medium mb-3
                                    <?php echo e(($booth->status ?? 'available') === 'available' ? 'text-green-600' :
                                       (($booth->status ?? 'available') === 'booked' ? 'text-red-600' : 'text-gray-600')); ?>">
                                    <?php echo e(($booth->status ?? 'available') === 'available' ? 'متاح' :
                                       (($booth->status ?? 'available') === 'booked' ? 'محجوز' : 'غير متاح')); ?>

                                </div>

                                <!-- Actions -->
                                <div class="flex flex-col gap-1">
                                    <a href="/admin/booths/<?php echo e($booth->id ?? 1); ?>/edit"
                                       class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">
                                        تعديل
                                    </a>
                                    <?php if(($booth->status ?? 'available') === 'booked'): ?>
                                        <a href="/admin/booths/<?php echo e($booth->id ?? 1); ?>/booking"
                                           class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded hover:bg-yellow-200">
                                            عرض الحجز
                                        </a>
                                    <?php endif; ?>
                                    <button onclick="deleteBooth(<?php echo e($booth->id ?? 1); ?>)"
                                            class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200">
                                        حذف
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <!-- Sample Booths for Demo -->
                            <?php for($i = 1; $i <= 20; $i++): ?>
                                <div class="border-2 rounded-lg p-4 text-center transition-all hover:shadow-md
                                    <?php echo e($i <= 5 ? 'border-red-300 bg-red-50' : 'border-green-300 bg-green-50'); ?>">

                                    <div class="text-lg font-bold mb-2 <?php echo e($i <= 5 ? 'text-red-800' : 'text-green-800'); ?>">
                                        جناح A-<?php echo e($i); ?>

                                    </div>

                                    <div class="text-sm text-gray-600 mb-3">
                                        <div>25 م²</div>
                                        <div>500 د.ك</div>
                                    </div>

                                    <div class="text-xs font-medium mb-3 <?php echo e($i <= 5 ? 'text-red-600' : 'text-green-600'); ?>">
                                        <?php echo e($i <= 5 ? 'محجوز' : 'متاح'); ?>

                                    </div>

                                    <div class="flex flex-col gap-1">
                                        <a href="/admin/booths/<?php echo e($i); ?>/edit"
                                           class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">
                                            تعديل
                                        </a>
                                        <?php if($i <= 5): ?>
                                            <a href="/admin/booths/<?php echo e($i); ?>/booking"
                                               class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded hover:bg-yellow-200">
                                                عرض الحجز
                                            </a>
                                        <?php endif; ?>
                                        <button onclick="deleteBooth(<?php echo e($i); ?>)"
                                                class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200">
                                            حذف
                                        </button>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">🎪</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e(count($booths ?? []) ?: 20); ?></div>
                            <div class="text-sm text-gray-500">إجمالي الأجنحة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">✅</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-green-600">15</div>
                            <div class="text-sm text-gray-500">أجنحة متاحة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📋</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-red-600">5</div>
                            <div class="text-sm text-gray-500">أجنحة محجوزة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">💰</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-blue-600">2,500</div>
                            <div class="text-sm text-gray-500">إجمالي الإيرادات (د.ك)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Script -->
    <script>
        function deleteBooth(id) {
            if (confirm('هل أنت متأكد من حذف هذا الجناح؟ سيتم حذف جميع الحجوزات المرتبطة به.')) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/booths/${id}`;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '<?php echo e(csrf_token()); ?>';

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/admin/exhibitions/booths.blade.php ENDPATH**/ ?>