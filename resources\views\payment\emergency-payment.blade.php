<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إكمال الدفع - Season Expo Kuwait</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .payment-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 600;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            width: 100%;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .amount-display {
            font-size: 2rem;
            font-weight: bold;
            color: #10b981;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                        <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                    </a>
                </div>
                
                <div class="flex items-center space-x-4" style="direction: ltr;">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        حسابي
                    </a>
                    <a href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        حجوزاتي
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-12">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">💳 إكمال الدفع</h1>
                <p class="mt-2 text-gray-600">حجز رقم #{{ $booking->id }}</p>
            </div>

            <!-- Payment Summary -->
            <div class="payment-card">
                <h2 class="text-xl font-bold text-gray-900 mb-4">📋 ملخص الحجز</h2>
                
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between">
                        <span class="text-gray-600">المعرض:</span>
                        <span class="font-semibold">{{ $booking->exhibition->title ?? 'غير محدد' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">رقم الجناح:</span>
                        <span class="font-semibold">{{ $booking->booth->booth_number ?? 'غير محدد' }}</span>
                    </div>
                    @if($booking->booth && $booking->booth->area)
                        <div class="flex justify-between">
                            <span class="text-gray-600">المساحة:</span>
                            <span class="font-semibold">{{ $booking->booth->area }} م²</span>
                        </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-gray-600">تاريخ الحجز:</span>
                        <span class="font-semibold">{{ $booking->created_at->format('d/m/Y') }}</span>
                    </div>
                </div>

                <hr class="my-4">
                
                <div class="amount-display">
                    {{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}
                </div>
            </div>

            <!-- Payment Method -->
            <div class="payment-card">
                <h2 class="text-xl font-bold text-gray-900 mb-4">💳 طريقة الدفع</h2>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-center">
                        <div class="text-blue-600 text-2xl ml-3">🏦</div>
                        <div>
                            <div class="font-semibold text-blue-900">دفع مبسط</div>
                            <div class="text-sm text-blue-700">سيتم تأكيد حجزك فوراً</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Actions -->
            <div class="payment-card">
                <div class="space-y-4">
                    <!-- Main Payment Button -->
                    <form method="POST" action="/emergency-complete-payment/{{ $booking->id }}">
                        @csrf
                        <button type="submit" class="btn btn-success" id="paymentBtn"
                                onclick="this.textContent='جاري المعالجة...'; this.disabled=true; this.form.submit();">
                            🔒 تأكيد الدفع وإتمام الحجز
                        </button>
                    </form>

                    <!-- Back Button -->
                    <div class="text-center pt-4">
                        <a href="/bookings/{{ $booking->id }}" class="text-gray-600 hover:text-gray-900">
                            ← العودة لتفاصيل الحجز
                        </a>
                    </div>
                </div>

                <!-- Security Badge -->
                <div class="flex items-center justify-center bg-green-50 p-3 rounded-lg mt-4">
                    <div class="text-green-600 text-lg ml-2">🔒</div>
                    <div class="text-sm text-green-700">
                        عملية آمنة ومشفرة
                    </div>
                </div>
            </div>

            <!-- Payment Info -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="font-bold text-blue-900 mb-3">ℹ️ معلومات مهمة</h3>
                <ul class="text-blue-800 space-y-2 text-sm">
                    <li>• سيتم تأكيد حجزك فور الضغط على زر التأكيد</li>
                    <li>• ستصلك رسالة تأكيد في صفحة تفاصيل الحجز</li>
                    <li>• يمكنك طباعة تفاصيل الحجز من الصفحة التالية</li>
                    <li>• في حالة وجود مشاكل، تواصل مع الدعم الفني</li>
                </ul>
            </div>

            <!-- Alternative Links -->
            <div class="text-center mt-8">
                <div class="space-y-2">
                    <p class="text-sm text-gray-600">روابط مفيدة:</p>
                    <div class="space-x-4" style="direction: ltr;">
                        <a href="/dashboard/reservations" class="text-blue-600 hover:text-blue-800 text-sm">جميع الحجوزات</a>
                        <a href="/booth-search-v2" class="text-blue-600 hover:text-blue-800 text-sm">البحث عن أجنحة أخرى</a>
                        <a href="/contact" class="text-blue-600 hover:text-blue-800 text-sm">اتصل بنا</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const paymentBtn = document.getElementById('paymentBtn');
            
            form.addEventListener('submit', function(e) {
                // Add loading state
                paymentBtn.innerHTML = '⏳ جاري المعالجة...';
                paymentBtn.disabled = true;
                paymentBtn.style.backgroundColor = '#6b7280';
                
                // Show processing message
                setTimeout(() => {
                    paymentBtn.innerHTML = '✅ تم الإرسال...';
                }, 1000);
            });
        });
    </script>
</body>
</html>
