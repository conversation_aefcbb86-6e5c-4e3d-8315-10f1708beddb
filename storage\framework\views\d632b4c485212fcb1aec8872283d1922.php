<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار السلايد</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-center mb-8">اختبار السلايد - 4 معارض</h1>
        
        <!-- Hero Slider Section -->
        <section class="relative h-96 overflow-hidden rounded-lg shadow-lg">
            <div class="flex transition-transform duration-500 ease-in-out h-full" id="hero-slider">
                <!-- Slide 1 -->
                <div class="min-w-full relative h-full">
                    <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://picsum.photos/1200/600?random=tech1'); background-color: #1e40af;"></div>
                    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
                    <div class="relative h-full flex items-center justify-center text-white">
                        <div class="text-center">
                            <h1 class="text-4xl md:text-6xl font-bold mb-4">معرض التكنولوجيا 2025</h1>
                            <p class="text-xl md:text-2xl mb-2">أحدث التقنيات والابتكارات التكنولوجية</p>
                            <p class="text-lg mb-6">📅 15-20 مارس 2025 | 📍 مركز الكويت الدولي للمعارض</p>
                            <div class="space-x-reverse space-x-4">
                                <a href="/exhibitions" class="bg-white text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    عرض التفاصيل
                                </a>
                                <a href="/login-simple" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 transition-colors">
                                    احجز جناحك
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 2 -->
                <div class="min-w-full relative h-full">
                    <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://picsum.photos/1200/600?random=fashion1'); background-color: #7c3aed;"></div>
                    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
                    <div class="relative h-full flex items-center justify-center text-white">
                        <div class="text-center">
                            <h1 class="text-4xl md:text-6xl font-bold mb-4">معرض الأزياء والموضة 2025</h1>
                            <p class="text-xl md:text-2xl mb-2">أحدث صيحات الموضة والأزياء العالمية</p>
                            <p class="text-lg mb-6">📅 20-25 أبريل 2025 | 📍 مركز الكويت الدولي للمعارض</p>
                            <div class="space-x-reverse space-x-4">
                                <a href="/exhibitions" class="bg-white text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    عرض التفاصيل
                                </a>
                                <a href="/login-simple" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 transition-colors">
                                    احجز جناحك
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 3 -->
                <div class="min-w-full relative h-full">
                    <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://picsum.photos/1200/600?random=cars1'); background-color: #dc2626;"></div>
                    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
                    <div class="relative h-full flex items-center justify-center text-white">
                        <div class="text-center">
                            <h1 class="text-4xl md:text-6xl font-bold mb-4">معرض السيارات 2025</h1>
                            <p class="text-xl md:text-2xl mb-2">أحدث موديلات السيارات والتقنيات الحديثة</p>
                            <p class="text-lg mb-6">📅 1-6 مايو 2025 | 📍 مركز الكويت الدولي للمعارض</p>
                            <div class="space-x-reverse space-x-4">
                                <a href="/exhibitions" class="bg-white text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    عرض التفاصيل
                                </a>
                                <a href="/login-simple" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 transition-colors">
                                    احجز جناحك
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 4 -->
                <div class="min-w-full relative h-full">
                    <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://picsum.photos/1200/600?random=food1'); background-color: #059669;"></div>
                    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
                    <div class="relative h-full flex items-center justify-center text-white">
                        <div class="text-center">
                            <h1 class="text-4xl md:text-6xl font-bold mb-4">معرض الطعام والمشروبات 2025</h1>
                            <p class="text-xl md:text-2xl mb-2">أشهى المأكولات والمشروبات العالمية</p>
                            <p class="text-lg mb-6">📅 10-15 يونيو 2025 | 📍 مركز الكويت الدولي للمعارض</p>
                            <div class="space-x-reverse space-x-4">
                                <a href="/exhibitions" class="bg-white text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    عرض التفاصيل
                                </a>
                                <a href="/login-simple" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 transition-colors">
                                    احجز جناحك
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slider Controls -->
            <button onclick="previousHeroSlide()" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 transition-all z-10">
                <svg class="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button onclick="nextHeroSlide()" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 transition-all z-10">
                <svg class="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Slider Indicators -->
            <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3 z-10">
                <button onclick="goToHeroSlide(0)" class="w-4 h-4 rounded-full bg-white bg-opacity-100 transition-all" id="hero-indicator-0"></button>
                <button onclick="goToHeroSlide(1)" class="w-4 h-4 rounded-full bg-white bg-opacity-60 hover:bg-opacity-100 transition-all" id="hero-indicator-1"></button>
                <button onclick="goToHeroSlide(2)" class="w-4 h-4 rounded-full bg-white bg-opacity-60 hover:bg-opacity-100 transition-all" id="hero-indicator-2"></button>
                <button onclick="goToHeroSlide(3)" class="w-4 h-4 rounded-full bg-white bg-opacity-60 hover:bg-opacity-100 transition-all" id="hero-indicator-3"></button>
            </div>
        </section>
        
        <div class="mt-8 text-center">
            <p class="text-lg text-gray-600 mb-4">هذا اختبار للسلايد مع 4 معارض ثابتة</p>
            <div class="space-x-4">
                <a href="/" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">الصفحة الرئيسية</a>
                <a href="/ar-test" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">اختبار العربية</a>
                <a href="/en-test" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700">اختبار الإنجليزية</a>
            </div>
        </div>
    </div>

    <script>
        // Hero Slider JavaScript
        let currentHeroSlide = 0;
        const totalHeroSlides = 4;

        function updateHeroSlider() {
            const slider = document.getElementById('hero-slider');
            const translateX = -currentHeroSlide * 100;
            slider.style.transform = `translateX(${translateX}%)`;

            // Update indicators
            for (let i = 0; i < totalHeroSlides; i++) {
                const indicator = document.getElementById(`hero-indicator-${i}`);
                if (indicator) {
                    if (i === currentHeroSlide) {
                        indicator.classList.remove('bg-opacity-60');
                        indicator.classList.add('bg-opacity-100');
                    } else {
                        indicator.classList.remove('bg-opacity-100');
                        indicator.classList.add('bg-opacity-60');
                    }
                }
            }
        }

        function nextHeroSlide() {
            currentHeroSlide = (currentHeroSlide + 1) % totalHeroSlides;
            updateHeroSlider();
        }

        function previousHeroSlide() {
            currentHeroSlide = (currentHeroSlide - 1 + totalHeroSlides) % totalHeroSlides;
            updateHeroSlider();
        }

        function goToHeroSlide(slideIndex) {
            currentHeroSlide = slideIndex;
            updateHeroSlider();
        }

        // Auto-advance slider
        setInterval(nextHeroSlide, 5000);

        // Initialize slider
        updateHeroSlider();
        
        console.log('Slider initialized with', totalHeroSlides, 'slides');
    </script>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/test-slider.blade.php ENDPATH**/ ?>