<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تعديل معرض {{ $exhibition->title ?? 'المعرض' }} - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                    <span class="mr-4 px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded">Admin</span>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/admin/exhibitions" class="text-blue-600 hover:text-blue-700">إدارة المعارض</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">تعديل المعرض</h1>
                        <p class="text-gray-600 mt-2">{{ $exhibition->title ?? 'معرض التكنولوجيا والابتكار 2024' }}</p>
                    </div>
                    <a href="/admin/exhibitions" class="text-blue-600 hover:text-blue-700">← العودة للمعارض</a>
                </div>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-green-600 text-xl ml-3">✅</span>
                        <p class="text-green-800 font-semibold">{{ session('success') }}</p>
                    </div>
                </div>
            @endif

            @if($errors->any())
                <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-red-600 text-xl ml-3">❌</span>
                        <div>
                            <p class="text-red-800 font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                            <ul class="text-red-700 list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Form -->
            <form method="POST" action="/admin/exhibitions/{{ $exhibition->id ?? 1 }}/update-safe" enctype="multipart/form-data" class="space-y-8">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">المعلومات الأساسية</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Title -->
                        <div class="md:col-span-2">
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان المعرض</label>
                            <input type="text" id="title" name="title" value="{{ old('title', $exhibition->title ?? '') }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="مثال: معرض التكنولوجيا والابتكار 2024">
                        </div>

                        <!-- Slug -->
                        <div class="md:col-span-2">
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">الرابط المختصر (Slug)</label>
                            <input type="text" id="slug" name="slug" value="{{ old('slug', $exhibition->slug ?? '') }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="tech-innovation-2024">
                            <p class="text-xs text-gray-500 mt-1">سيتم استخدامه في الرابط: /exhibitions/{{ $exhibition->slug ?? 'slug' }}</p>
                        </div>



                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                            <select id="status" name="status" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="draft" {{ old('status', $exhibition->status ?? '') == 'draft' ? 'selected' : '' }}>مسودة</option>
                                <option value="published" {{ old('status', $exhibition->status ?? '') == 'published' ? 'selected' : '' }}>منشور</option>
                                <option value="cancelled" {{ old('status', $exhibition->status ?? '') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                <option value="completed" {{ old('status', $exhibition->status ?? '') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                            </select>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mt-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">وصف المعرض</label>
                        <textarea id="description" name="description" rows="4" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="وصف شامل للمعرض وأهدافه...">{{ old('description', $exhibition->description ?? '') }}</textarea>
                    </div>
                </div>

                <!-- Dates and Timing -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">التواريخ والأوقات</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Start Date -->
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية</label>
                            <input type="date" id="start_date" name="start_date"
                                   value="{{ old('start_date', isset($exhibition->start_date) ? $exhibition->start_date->format('Y-m-d') : '') }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- End Date -->
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ النهاية</label>
                            <input type="date" id="end_date" name="end_date"
                                   value="{{ old('end_date', isset($exhibition->end_date) ? $exhibition->end_date->format('Y-m-d') : '') }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Registration Deadline -->
                        <div>
                            <label for="registration_deadline" class="block text-sm font-medium text-gray-700 mb-2">آخر موعد للتسجيل</label>
                            <input type="date" id="registration_deadline" name="registration_deadline"
                                   value="{{ old('registration_deadline', isset($exhibition->registration_deadline) ? $exhibition->registration_deadline->format('Y-m-d') : '') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Max Exhibitors -->
                        <div>
                            <label for="max_exhibitors" class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى للعارضين</label>
                            <input type="number" id="max_exhibitors" name="max_exhibitors"
                                   value="{{ old('max_exhibitors', $exhibition->max_exhibitors ?? 100) }}" min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">معلومات الاتصال</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Contact Email -->
                        <div>
                            <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                            <input type="email" id="contact_email" name="contact_email"
                                   value="{{ old('contact_email', $exhibition->contact_email ?? '') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<EMAIL>">
                        </div>

                        <!-- Contact Phone -->
                        <div>
                            <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                            <input type="tel" id="contact_phone" name="contact_phone"
                                   value="{{ old('contact_phone', $exhibition->contact_phone ?? '') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="+965 1234 5678">
                        </div>
                    </div>
                </div>

                <!-- Exhibition Statistics -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-blue-900 mb-4">إحصائيات المعرض</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <div class="text-sm text-blue-600 font-medium">إجمالي الأجنحة</div>
                            <div class="text-2xl font-bold text-blue-900">{{ $exhibition->booths_count ?? 0 }}</div>
                        </div>
                        <div>
                            <div class="text-sm text-blue-600 font-medium">الأجنحة المحجوزة</div>
                            <div class="text-2xl font-bold text-blue-900">{{ $exhibition->booked_booths ?? 0 }}</div>
                        </div>
                        <div>
                            <div class="text-sm text-blue-600 font-medium">نسبة الإشغال</div>
                            <div class="text-2xl font-bold text-blue-900">
                                {{ $exhibition->booths_count > 0 ? round(($exhibition->booked_booths ?? 0) / $exhibition->booths_count * 100) : 0 }}%
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-between items-center">
                    <div class="flex gap-4">
                        <a href="/admin/exhibitions"
                           class="px-6 py-3 bg-gray-500 text-white rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                            إلغاء
                        </a>
                        <button type="submit"
                                class="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            حفظ التغييرات
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="flex gap-2">
                        <a href="/admin/exhibitions/{{ $exhibition->id ?? 1 }}/booths"
                           class="px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors">
                            إدارة الأجنحة
                        </a>
                        <a href="/admin/exhibitions/{{ $exhibition->id ?? 1 }}/layout"
                           class="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-colors">
                            تخطيط المعرض
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Auto-generate slug script -->
    <script>
        document.getElementById('title').addEventListener('input', function() {
            const title = this.value;
            const slug = title
                .toLowerCase()
                .replace(/[أ-ي]/g, '') // Remove Arabic characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/[^\w\-]+/g, '') // Remove non-word chars
                .replace(/\-\-+/g, '-') // Replace multiple hyphens
                .replace(/^-+/, '') // Trim hyphens from start
                .replace(/-+$/, ''); // Trim hyphens from end

            document.getElementById('slug').value = slug;
        });
    </script>
</body>
</html>
