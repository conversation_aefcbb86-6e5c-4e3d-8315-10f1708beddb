<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Signature related columns
            $table->longText('signature_data')->nullable()->after('status');
            $table->timestamp('signature_date')->nullable()->after('signature_data');
            $table->string('signature_ip')->nullable()->after('signature_date');
            $table->text('signature_user_agent')->nullable()->after('signature_ip');
            $table->text('declaration_text')->nullable()->after('signature_user_agent');
            $table->boolean('declaration_accepted')->default(false)->after('declaration_text');

            // Company information columns
            $table->string('trade_name')->nullable()->after('declaration_accepted');
            $table->string('license_holder_name')->nullable()->after('trade_name');
            $table->string('commercial_license_number')->nullable()->after('license_holder_name');
            $table->date('license_date')->nullable()->after('commercial_license_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn([
                'signature_data',
                'signature_date',
                'signature_ip',
                'signature_user_agent',
                'declaration_text',
                'declaration_accepted',
                'trade_name',
                'license_holder_name',
                'commercial_license_number',
                'license_date'
            ]);
        });
    }
};
