<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إدارة المعارض - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                    <span class="mr-4 px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded">Admin</span>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/admin/exhibitions" class="text-blue-600 font-semibold">إدارة المعارض</a>
                    <a href="/admin/users" class="text-gray-600 hover:text-gray-900">إدارة المستخدمين</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                    <form method="POST" action="/logout" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-gray-600 hover:text-gray-900">تسجيل الخروج</button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">إدارة المعارض</h1>
                <p class="text-gray-600 mt-2">إنشاء وإدارة المعارض والأجنحة</p>
            </div>

            <!-- Success/Error Messages -->
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-green-600 text-xl ml-3">✅</span>
                        <p class="text-green-800 font-semibold"><?php echo e(session('success')); ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-red-600 text-xl ml-3">❌</span>
                        <div>
                            <p class="text-red-800 font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                            <ul class="text-red-700 list-disc list-inside">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="mb-8 flex flex-wrap gap-4">
                <a href="/admin/exhibitions/create"
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    ➕ إنشاء معرض جديد
                </a>
                <a href="/admin/exhibitions/bulk-create-booths"
                   class="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    🏢 إنشاء أجنحة متعددة
                </a>
            </div>

            <!-- Exhibitions List -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">قائمة المعارض</h2>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    المعرض
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    التواريخ
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الأجنحة
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الحالة
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php $__empty_1 = true; $__currentLoopData = $exhibitions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exhibition): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <span class="text-blue-600 font-semibold">🏢</span>
                                                </div>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo e($exhibition->title ?? 'معرض تجريبي'); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo e($exhibition->location ?? 'الكويت'); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php echo e(isset($exhibition->start_date) ? $exhibition->start_date->format('d/m/Y') : '01/01/2024'); ?>

                                        </div>
                                        <div class="text-sm text-gray-500">
                                            إلى <?php echo e(isset($exhibition->end_date) ? $exhibition->end_date->format('d/m/Y') : '05/01/2024'); ?>

                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php echo e($exhibition->booths_count ?? 0); ?> جناح
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo e($exhibition->available_booths ?? 0); ?> متاح
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php echo e(($exhibition->status ?? 'active') === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                                            <?php echo e(($exhibition->status ?? 'active') === 'active' ? 'نشط' : 'غير نشط'); ?>

                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-reverse space-x-2">
                                            <a href="/admin/exhibitions/<?php echo e($exhibition->id ?? 1); ?>/booths"
                                               class="text-blue-600 hover:text-blue-900">إدارة الأجنحة</a>
                                            <a href="/admin/exhibitions/<?php echo e($exhibition->id ?? 1); ?>/edit"
                                               class="text-indigo-600 hover:text-indigo-900">تعديل</a>
                                            <button onclick="deleteExhibition(<?php echo e($exhibition->id ?? 1); ?>)"
                                                    class="text-red-600 hover:text-red-900">حذف</button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                        لا توجد معارض حالياً. <a href="/admin/exhibitions/create" class="text-blue-600 hover:text-blue-700">إنشاء معرض جديد</a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">🏢</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_exhibitions'] ?? 3); ?></div>
                            <div class="text-sm text-gray-500">إجمالي المعارض</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">🎪</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_booths'] ?? 45); ?></div>
                            <div class="text-sm text-gray-500">إجمالي الأجنحة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">✅</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['available_booths'] ?? 32); ?></div>
                            <div class="text-sm text-gray-500">أجنحة متاحة</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📋</span>
                        </div>
                        <div class="mr-4">
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_bookings'] ?? 13); ?></div>
                            <div class="text-sm text-gray-500">إجمالي الحجوزات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Script -->
    <script>
        function deleteExhibition(id) {
            if (confirm('هل أنت متأكد من حذف هذا المعرض؟ سيتم حذف جميع الأجنحة والحجوزات المرتبطة به.')) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/exhibitions/${id}`;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '<?php echo e(csrf_token()); ?>';

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/admin/exhibitions/index.blade.php ENDPATH**/ ?>