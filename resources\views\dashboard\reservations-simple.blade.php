<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('حجوزاتي') }} - {{ config('app.name', 'Season Expo Kuwait') }}</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
        }
        .booking-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            background: white;
            margin-bottom: 20px;
        }
        .booking-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 9999px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-confirmed {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-cancelled {
            background-color: #fee2e2;
            color: #dc2626;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2563eb;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-success:hover {
            background-color: #059669;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        @media print {
            nav, .no-print { display: none !important; }
            body { background: white !important; }
            .booking-card { border: 1px solid #ccc !important; box-shadow: none !important; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="/images/logo.png" alt="Season Expo Kuwait" onerror="this.style.display='none'">
                        <span class="ml-2 text-xl font-bold text-gray-900">Season Expo Kuwait</span>
                    </a>
                </div>

                <div class="flex items-center space-x-4" style="direction: ltr;">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('حسابي') }}
                    </a>
                    <a href="/dashboard/booth-search" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        {{ __('البحث عن الأجنحة') }}
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">📋 {{ __('حجوزاتي') }}</h1>
                <p class="mt-2 text-gray-600">{{ __('عرض وإدارة جميع حجوزاتك') }}</p>
            </div>

            <!-- Filter Tabs -->
            <div class="mb-6 no-print">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 rtl:space-x-reverse">
                        <a href="?status=" class="py-2 px-1 border-b-2 {{ request('status') == '' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700' }} font-medium text-sm">
                            {{ __('جميع الحجوزات') }}
                        </a>
                        <a href="?status=pending" class="py-2 px-1 border-b-2 {{ request('status') == 'pending' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700' }} font-medium text-sm">
                            {{ __('قيد الانتظار') }}
                        </a>
                        <a href="?status=confirmed" class="py-2 px-1 border-b-2 {{ request('status') == 'confirmed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700' }} font-medium text-sm">
                            {{ __('مؤكدة') }}
                        </a>
                        <a href="?status=cancelled" class="py-2 px-1 border-b-2 {{ request('status') == 'cancelled' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700' }} font-medium text-sm">
                            {{ __('ملغية') }}
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Results Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-900">
                    {{ __('إجمالي الحجوزات') }}: {{ $bookings->total() }}
                </h2>

                <!-- Print Button -->
                <button onclick="window.print()" class="btn btn-secondary no-print">
                    🖨️ {{ __('طباعة') }}
                </button>
            </div>

            <!-- Bookings List -->
            @if($bookings->count() > 0)
                <div class="space-y-6">
                    @foreach($bookings as $booking)
                        <div class="booking-card">
                            <!-- Booking Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        {{ __('حجز رقم') }} #{{ $booking->id }}
                                    </h3>
                                    <p class="text-sm text-gray-600">
                                        {{ __('تاريخ الحجز') }}: {{ $booking->created_at->format('d/m/Y H:i') }}
                                    </p>
                                </div>

                                <span class="status-badge status-{{ $booking->status }}">
                                    @if($booking->status == 'pending') {{ __('قيد الانتظار') }}
                                    @elseif($booking->status == 'confirmed') {{ __('مؤكد') }}
                                    @elseif($booking->status == 'cancelled') {{ __('ملغي') }}
                                    @else {{ $booking->status }}
                                    @endif
                                </span>
                            </div>

                            <!-- Booking Details -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                                <!-- Exhibition Info -->
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">{{ __('معلومات المعرض') }}</h4>
                                    <p class="text-sm text-gray-600">
                                        <strong>{{ __('المعرض') }}:</strong> {{ $booking->exhibition->title ?? __('غير محدد') }}
                                    </p>
                                    @if($booking->exhibition && $booking->exhibition->start_date)
                                        <p class="text-sm text-gray-600">
                                            <strong>{{ __('تاريخ البداية') }}:</strong> {{ $booking->exhibition->start_date->format('d/m/Y') }}
                                        </p>
                                    @endif
                                </div>

                                <!-- Booth Info -->
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">{{ __('معلومات الجناح') }}</h4>
                                    <p class="text-sm text-gray-600">
                                        <strong>{{ __('رقم الجناح') }}:</strong> {{ $booking->booth->booth_number ?? __('غير محدد') }}
                                    </p>
                                    @if($booking->booth && $booking->booth->area)
                                        <p class="text-sm text-gray-600">
                                            <strong>{{ __('المساحة') }}:</strong> {{ $booking->booth->area }} {{ __('م²') }}
                                        </p>
                                    @endif
                                </div>

                                <!-- Payment Info -->
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">{{ __('معلومات الدفع') }}</h4>
                                    <p class="text-sm text-gray-600">
                                        <strong>{{ __('المبلغ الإجمالي') }}:</strong>
                                        <span class="text-green-600 font-semibold">{{ number_format($booking->total_amount) }} {{ $booking->currency ?? 'د.ك' }}</span>
                                    </p>
                                    <p class="text-sm text-gray-600">
                                        <strong>{{ __('حالة الدفع') }}:</strong>
                                        @if($booking->status == 'confirmed')
                                            <span class="text-green-600">{{ __('مدفوع') }}</span>
                                        @else
                                            <span class="text-yellow-600">{{ __('غير مدفوع') }}</span>
                                        @endif
                                    </p>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-2 rtl:space-x-reverse pt-4 border-t border-gray-200 no-print">
                                <a href="/bookings/{{ $booking->id }}" class="btn btn-primary">
                                    👁️ {{ __('عرض التفاصيل') }}
                                </a>

                                @if($booking->status == 'pending' || $booking->status == 'pending_payment')
                                    @php
                                        $hasSignature = \App\Models\DigitalSignature::where('document_id', 'BOOKING-' . $booking->id)
                                            ->where('user_id', Auth::id())
                                            ->exists();
                                    @endphp

                                    @if(!$hasSignature)
                                        <a href="/digital-signature/{{ $booking->id }}" class="btn btn-warning">
                                            ✍️ {{ __('التوقيع الإلكتروني') }}
                                        </a>
                                    @else
                                        <a href="/payment/myfatoorah/{{ $booking->id }}" class="btn btn-success">
                                            💳 {{ __('إكمال الدفع') }}
                                        </a>
                                    @endif
                                @endif

                                @if($booking->status == 'confirmed')
                                    <a href="/bookings/{{ $booking->id }}/invoice" class="btn btn-secondary">
                                        📄 {{ __('تحميل الفاتورة') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($bookings->hasPages())
                    <div class="mt-8 no-print">
                        {{ $bookings->appends(request()->query())->links() }}
                    </div>
                @endif
            @else
                <!-- No Bookings -->
                <div class="text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">📋</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('لا توجد حجوزات') }}</h3>
                    <p class="text-gray-600 mb-6">{{ __('لم تقم بأي حجوزات بعد. ابدأ بالبحث عن الأجنحة المتاحة.') }}</p>

                    <a href="/dashboard/booth-search" class="btn btn-primary">
                        🔍 {{ __('البحث عن الأجنحة') }}
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Debug Info (only in debug mode) -->
    @if(config('app.debug'))
        <div class="fixed bottom-4 right-4 bg-black text-white p-2 rounded text-xs no-print">
            Version: {{ time() }} | Bookings: {{ $bookings->total() }}
        </div>
    @endif
</body>
</html>
