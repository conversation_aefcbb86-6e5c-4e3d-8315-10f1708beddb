<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->text('signature_data')->nullable();
            $table->timestamp('signature_date')->nullable();
            $table->string('signature_ip')->nullable();
            $table->text('signature_user_agent')->nullable();
            $table->string('trade_name')->nullable();
            $table->string('license_holder_name')->nullable();
            $table->string('commercial_license_number')->nullable();
            $table->date('license_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn([
                'signature_data',
                'signature_date',
                'signature_ip',
                'signature_user_agent',
                'trade_name',
                'license_holder_name',
                'commercial_license_number',
                'license_date'
            ]);
        });
    }
};
