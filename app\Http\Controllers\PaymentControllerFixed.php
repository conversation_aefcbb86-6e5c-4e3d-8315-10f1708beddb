<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentControllerFixed extends Controller
{
    private $apiKey;
    private $baseUrl;

    public function __construct()
    {
        $this->apiKey = env('MYFATOORAH_API_KEY');
        $this->baseUrl = 'https://apitest.myfatoorah.com';
    }

    /**
     * Initiate payment for a booking (WORKING VERSION)
     */
    public function initiatePayment(Request $request)
    {
        try {
            $bookingId = $request->input('booking_id') ?? $request->route('booking');
            $booking = Booking::with(['user', 'booth', 'exhibition'])->findOrFail($bookingId);

            // Use authenticated user
            $currentUserId = auth()->check() ? auth()->user()->id : 1;

            // Check ownership
            if ($booking->user_id !== $currentUserId) {
                return back()->withErrors(['payment' => 'غير مصرح لك بالوصول لهذا الحجز']);
            }

            // Check booking status
            if (!in_array($booking->status, ['pending', 'confirmed'])) {
                return back()->withErrors(['payment' => 'لا يمكن الدفع لهذا الحجز']);
            }

            // Check API Key
            if (!$this->apiKey) {
                return back()->withErrors(['payment' => 'إعدادات الدفع غير مكتملة']);
            }

            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => $booking->user_id,
                'amount' => $booking->total_amount,
                'currency' => 'KWD',
                'status' => 'pending',
                'payment_method' => 'myfatoorah',
                'gateway' => 'MyFatoorah',
            ]);

            // Prepare payment data (SIMPLIFIED)
            $paymentData = [
                'PaymentMethodId' => 1, // K-Net
                'CustomerName' => $booking->user->name ?? 'Customer',
                'InvoiceValue' => (float) $booking->total_amount,
                'DisplayCurrencyIso' => 'KWD',
                'MobileCountryCode' => '+965',
                'CustomerMobile' => $booking->user->phone ?? '12345678',
                'CustomerEmail' => $booking->user->email ?? '<EMAIL>',
                'CallBackUrl' => url('/payment/callback?booking_id=' . $booking->id),
                'ErrorUrl' => url('/payment/error?booking_id=' . $booking->id),
                'Language' => 'ar',
                'CustomerReference' => 'BOOKING-' . $booking->id . '-' . time(),
                'NotificationOption' => 'LNK',
                'InvoiceItems' => [
                    [
                        'ItemName' => "Booth {$booking->booth->booth_number} - {$booking->exhibition->title}",
                        'Quantity' => 1,
                        'UnitPrice' => (float) $booking->total_amount,
                    ]
                ]
            ];

            // Log request
            Log::info('MyFatoorah Payment Request (Fixed)', [
                'booking_id' => $booking->id,
                'amount' => $booking->total_amount,
                'api_key_length' => strlen($this->apiKey),
                'url' => $this->baseUrl . '/v2/ExecutePayment'
            ]);

            // Call MyFatoorah API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->baseUrl . '/v2/ExecutePayment');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->apiKey,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // Log response
            Log::info('MyFatoorah API Response (Fixed)', [
                'http_code' => $httpCode,
                'curl_error' => $error,
                'response_length' => strlen($response),
                'response_preview' => substr($response, 0, 200)
            ]);

            // Handle errors
            if ($error) {
                throw new Exception('cURL Error: ' . $error);
            }

            if ($httpCode === 401) {
                throw new Exception('API Key غير صحيح أو منتهي الصلاحية');
            }

            if ($httpCode !== 200) {
                throw new Exception('HTTP Error ' . $httpCode . ': ' . substr($response, 0, 100));
            }

            $responseData = json_decode($response, true);

            if (!$responseData) {
                throw new Exception('Invalid JSON response from MyFatoorah');
            }

            if (!isset($responseData['IsSuccess']) || !$responseData['IsSuccess']) {
                $errorMsg = $responseData['Message'] ?? 'Unknown API error';
                throw new Exception('MyFatoorah Error: ' . $errorMsg);
            }

            $paymentUrl = $responseData['Data']['PaymentURL'] ?? null;

            if (!$paymentUrl) {
                throw new Exception('No payment URL received');
            }

            // Update payment record
            $payment->update([
                'gateway_transaction_id' => $responseData['Data']['InvoiceId'] ?? 'INVOICE-' . time(),
                'gateway_response' => json_encode($responseData),
            ]);

            // Log success
            Log::info('Payment URL Generated (Fixed)', [
                'booking_id' => $booking->id,
                'payment_url' => $paymentUrl,
                'invoice_id' => $responseData['Data']['InvoiceId'] ?? 'Unknown'
            ]);

            // Redirect to MyFatoorah
            return redirect()->away($paymentUrl);

        } catch (Exception $e) {
            Log::error('Payment Failed (Fixed)', [
                'booking_id' => $bookingId ?? 'Unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return back()->withErrors(['payment' => 'فشل في الدفع: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle payment callback
     */
    public function paymentCallback(Request $request): RedirectResponse
    {
        // Handle successful payment
        $bookingId = $request->query('booking_id');
        
        if ($bookingId) {
            $booking = Booking::find($bookingId);
            if ($booking) {
                $booking->update(['status' => 'confirmed']);
            }
        }

        return redirect()->route('dashboard')->with('success', 'تم الدفع بنجاح');
    }

    /**
     * Handle payment error
     */
    public function paymentError(Request $request): RedirectResponse
    {
        return redirect()->route('dashboard')->withErrors(['payment' => 'فشل في عملية الدفع']);
    }
}
