<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إنشاء حساب جديد</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    <link rel="manifest" href="/images/site.webmanifest">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .bg-pattern { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>'); }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 bg-pattern">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="flex justify-center mb-4">
                    <img src="/images/logo-white.png" alt="Season Expo" class="h-16 w-auto">
                </div>
                <h2 class="text-3xl font-bold text-white mb-2">منصة المعارض</h2>
                <p class="text-blue-100">إنشاء حساب جديد في منصة المعارض الرائدة</p>
            </div>

            <!-- Registration Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <form method="POST" action="/register" class="space-y-6">
                    @csrf

                    <!-- Success/Error Messages -->
                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <span class="text-red-600 text-xl ml-3">❌</span>
                                <div>
                                    <p class="text-red-800 font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                                    <ul class="text-red-700 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                        <input type="text" id="name" name="name" value="{{ old('name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل اسمك الكامل">
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" value="{{ old('email') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="+965 1234 5678">
                        @error('phone')
                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Company -->
                    <div>
                        <label for="company" class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة (اختياري)</label>
                        <input type="text" id="company" name="company" value="{{ old('company') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="اسم شركتك أو مؤسستك">
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" id="password" name="password" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل كلمة مرور قوية" onchange="checkPasswordMatch()">
                        <p class="text-xs text-gray-500 mt-1">يجب أن تحتوي على 8 أحرف على الأقل</p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                        <input type="password" id="password_confirmation" name="password_confirmation" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أعد إدخال كلمة المرور" onchange="checkPasswordMatch()">
                        <div id="password-match-message" class="mt-1 text-xs"></div>
                    </div>

                    <!-- Account Type -->
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">نوع الحساب</label>
                        <select id="role" name="role" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر نوع الحساب</option>
                            <option value="exhibitor" {{ old('role') == 'exhibitor' ? 'selected' : '' }}>عارض (Exhibitor)</option>
                            <option value="organizer" {{ old('role') == 'organizer' ? 'selected' : '' }}>منظم معارض (Organizer)</option>
                        </select>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="flex items-start">
                        <input type="checkbox" id="terms" name="terms" required
                               class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="terms" class="mr-2 text-sm text-gray-700">
                            أوافق على <a href="#" class="text-blue-600 hover:text-blue-700 underline">الشروط والأحكام</a>
                            و <a href="#" class="text-blue-600 hover:text-blue-700 underline">سياسة الخصوصية</a>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                        إنشاء الحساب
                    </button>
                </form>

                <!-- Login Link -->
                <div class="mt-6 text-center">
                    <p class="text-gray-600">
                        لديك حساب بالفعل؟
                        <a href="/login-simple" class="text-blue-600 hover:text-blue-700 font-semibold">تسجيل الدخول</a>
                    </p>
                </div>

                <!-- Forgot Password Link -->
                <div class="mt-4 text-center">
                    <p class="text-gray-600">
                        نسيت كلمة المرور؟
                        <a href="/reset-password-form" class="text-blue-600 hover:text-blue-700 font-semibold">🔑 استعادة كلمة المرور</a>
                    </p>
                </div>
            </div>

            <!-- Footer Links -->
            <div class="text-center">
                <div class="flex justify-center space-x-reverse space-x-6 text-blue-100">
                    <a href="/" class="hover:text-white">الصفحة الرئيسية</a>
                    <a href="/exhibitions" class="hover:text-white">المعارض</a>
                    <a href="/reset-password-form" class="hover:text-white">🔑 نسيت كلمة المرور؟</a>
                    <a href="#" class="hover:text-white">المساعدة</a>
                </div>
                <p class="mt-4 text-blue-200 text-sm">© 2024 جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <script>
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;
            const messageDiv = document.getElementById('password-match-message');
            const submitButton = document.querySelector('button[type="submit"]');

            if (confirmPassword === '') {
                messageDiv.innerHTML = '';
                messageDiv.className = 'mt-1 text-xs';
                return;
            }

            if (password === confirmPassword) {
                messageDiv.innerHTML = '✅ كلمات المرور متطابقة';
                messageDiv.className = 'mt-1 text-xs text-green-600';
                submitButton.disabled = false;
                submitButton.className = 'w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors';
            } else {
                messageDiv.innerHTML = '❌ كلمات المرور غير متطابقة';
                messageDiv.className = 'mt-1 text-xs text-red-600';
                submitButton.disabled = true;
                submitButton.className = 'w-full bg-gray-400 text-white py-3 px-4 rounded-lg font-semibold cursor-not-allowed';
            }
        }

        // Validate form before submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;
            const phone = document.getElementById('phone').value;

            // Check phone number
            if (!phone.trim()) {
                e.preventDefault();
                alert('رقم الهاتف مطلوب');
                document.getElementById('phone').focus();
                return false;
            }

            // Check password match
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                document.getElementById('password_confirmation').focus();
                return false;
            }

            // Check password length
            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل');
                document.getElementById('password').focus();
                return false;
            }
        });

        // Real-time phone validation
        document.getElementById('phone').addEventListener('input', function() {
            const phone = this.value;
            const phoneLabel = document.querySelector('label[for="phone"]');

            if (phone.trim() === '') {
                phoneLabel.innerHTML = 'رقم الهاتف <span class="text-red-500">*</span>';
                this.style.borderColor = '#ef4444';
            } else {
                phoneLabel.innerHTML = 'رقم الهاتف <span class="text-red-500">*</span>';
                this.style.borderColor = '#d1d5db';
            }
        });
    </script>
</body>
</html>
