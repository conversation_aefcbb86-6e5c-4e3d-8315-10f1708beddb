<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>الملف الشخصي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <img src="/images/logo.png" alt="Season Expo" class="h-10 w-auto">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">حسابي</a>
                    <a href="/profile" class="text-blue-600 font-semibold">الملف الشخصي</a>
                    <form method="POST" action="/logout" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-gray-600 hover:text-gray-900">تسجيل الخروج</button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">الملف الشخصي</h1>
                <p class="text-gray-600 mt-2">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
            </div>

            <!-- Success/Error Messages -->
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-green-600 text-xl ml-3">✅</span>
                        <p class="text-green-800 font-semibold"><?php echo e(session('success')); ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <span class="text-red-600 text-xl ml-3">❌</span>
                        <div>
                            <p class="text-red-800 font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                            <ul class="text-red-700 list-disc list-inside">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">المعلومات الشخصية</h2>
                        </div>
                        <form method="POST" action="/profile/update" class="p-6 space-y-6">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>

                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                                <input type="text" id="name" name="name" value="<?php echo e(old('name', $user->name)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       required>
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       required>
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone" value="<?php echo e(old('phone', $user->phone)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="+965 1234 5678">
                            </div>

                            <!-- Company -->
                            <div>
                                <label for="company" class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                                <input type="text" id="company" name="company" value="<?php echo e(old('company', $user->company)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="اسم شركتك أو مؤسستك">
                            </div>

                            <!-- Address -->
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                <textarea id="address" name="address" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="العنوان الكامل"><?php echo e(old('address', $user->address)); ?></textarea>
                            </div>

                            <!-- Role (Read-only) -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحساب</label>
                                <div class="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-600">
                                    <?php switch($user->role):
                                        case ('admin'): ?>
                                            مدير النظام
                                            <?php break; ?>
                                        <?php case ('organizer'): ?>
                                            منظم المعارض
                                            <?php break; ?>
                                        <?php case ('exhibitor'): ?>
                                            عارض
                                            <?php break; ?>
                                        <?php default: ?>
                                            مستخدم
                                    <?php endswitch; ?>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit"
                                        class="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Change Password Section -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-8">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">تغيير كلمة المرور</h2>
                        </div>
                        <form method="POST" action="/profile/password" class="p-6 space-y-6">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>

                            <!-- Current Password -->
                            <div>
                                <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                                <input type="password" id="current_password" name="current_password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       required>
                            </div>

                            <!-- New Password -->
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                                <input type="password" id="password" name="password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       required>
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                                <input type="password" id="password_confirmation" name="password_confirmation"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       required>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit"
                                        class="bg-red-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                                    تغيير كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Profile Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">ملخص الحساب</h2>
                        </div>
                        <div class="p-6 space-y-4">
                            <!-- Profile Picture Placeholder -->
                            <div class="text-center">
                                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl text-blue-600">👤</span>
                                </div>
                                <h3 class="font-semibold text-gray-900"><?php echo e($user->name); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo e($user->email); ?></p>
                            </div>

                            <!-- Account Stats -->
                            <div class="border-t pt-4">
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">تاريخ التسجيل:</span>
                                        <span class="font-semibold"><?php echo e($user->created_at->format('d/m/Y')); ?></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">آخر تحديث:</span>
                                        <span class="font-semibold"><?php echo e($user->updated_at->format('d/m/Y')); ?></span>
                                    </div>
                                    <?php if($user->email_verified_at): ?>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">البريد مؤكد:</span>
                                            <span class="text-green-600 font-semibold">✅ نعم</span>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">البريد مؤكد:</span>
                                            <span class="text-red-600 font-semibold">❌ لا</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="border-t pt-4">
                                <h4 class="font-semibold text-gray-900 mb-3">إجراءات سريعة</h4>
                                <div class="space-y-2">
                                    <a href="/dashboard" class="block w-full text-center bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors">
                                        حسابي
                                    </a>
                                    <a href="/bookings" class="block w-full text-center bg-green-50 text-green-600 py-2 rounded-lg hover:bg-green-100 transition-colors">
                                        حجوزاتي
                                    </a>
                                    <a href="/exhibitions" class="block w-full text-center bg-purple-50 text-purple-600 py-2 rounded-lg hover:bg-purple-100 transition-colors">
                                        المعارض
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2_2\resources\views/profile/edit.blade.php ENDPATH**/ ?>